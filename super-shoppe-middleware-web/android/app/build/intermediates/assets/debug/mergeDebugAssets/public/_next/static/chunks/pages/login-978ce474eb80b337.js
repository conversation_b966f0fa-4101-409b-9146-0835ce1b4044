(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[459],{3236:function(e,r,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/login",function(){return n(1813)}])},5621:function(e,r,n){"use strict";n.d(r,{GI:function(){return u},N:function(){return i},d4:function(){return o},ic:function(){return a}});n(8139),n(2828);var t=n(240),s=n.n(t);function a(e){if(e.data){if("string"===typeof e.data)return e.data;if("object"===typeof e.data){if(e.data.error)return e.data.error;if(e.data.message)return e.data.message}}}var i=function(e){return e?(e=e.replaceAll(" ","").replaceAll("-","")).length<=0?null:s()(e,"+60")?e.replace("+60","60"):s()(e,"01")?e.replace("01","601"):e:null},o=function(e){return e?e.replaceAll(" ","").toLowerCase():null},u=function(e){return encodeURIComponent(e.replace(/"/g,'\\"').replace(/'/g,"\\'"))}},5402:function(e,r,n){"use strict";var t=n(7568),s=n(6042),a=n(9396),i=n(7582),o=n(9669),u=n.n(o),c=n(4286),l=n(1163),d=n.n(l),f=n(8139),p=u().create({baseURL:"https://proceeding-social-tiffany-doctors.trycloudflare.com",timeout:3e4,headers:{Accept:"application/json","Content-Type":"application/json"}});p.interceptors.request.use(function(){var e=(0,t.Z)((function(e){var r,n;return(0,i.__generator)(this,(function(t){switch(t.label){case 0:return[4,(0,c.LP)()];case 1:return n=t.sent(),e.headers=(0,s.Z)((0,a.Z)((0,s.Z)({},e.headers),{Authorization:n?"Bearer ".concat(n):"Bearer "}),(null===(r=e.baseURL)||void 0===r?void 0:r.includes("ngrok"))?{"ngrok-skip-browser-warning":"1"}:{}),[2,e]}}))}));return function(r){return e.apply(this,arguments)}}(),(function(e){return Promise.reject(e)})),p.interceptors.response.use((function(e){return e}),(function(e){return e.response&&403==e.response.status&&d().push({pathname:f.Z.LOGOUT,query:{force:1}}),Promise.reject(e)})),r.Z=p},1813:function(e,r,n){"use strict";n.r(r),n.d(r,{default:function(){return v}});var t=n(6042),s=n(9396),a=n(5893),i=n(7568),o=n(7582),u=n(8126),c=n(8767),l=n(5538),d=n(5402),f=n(4758),p=n(4286),m=n(5621),h=n(1163),g=n(8139);function x(){return(x=(0,i.Z)((function(e){return(0,o.__generator)(this,(function(r){switch(r.label){case 0:return[4,d.Z.post(f.P.LOGIN,e)];case 1:return[2,r.sent().data]}}))}))).apply(this,arguments)}var b=function(){var e=(0,c.useQueryClient)(),r=(0,h.useRouter)(),n=(0,u.l8)(),t=n.authorize;n.unauthorize;return(0,c.useMutation)((function(e){return function(e){return x.apply(this,arguments)}({email:e.email,password:e.password})}),{onSuccess:function(){var n=(0,i.Z)((function(n){return(0,o.__generator)(this,(function(s){return n.data&&n.data.token&&((0,p.uB)(n.data.token),t(),e.invalidateQueries(f.P.ME),e.invalidateQueries(f.P.CART),e.invalidateQueries(f.P.REFER_CODE),r.push(g.Z.HOME)),[2]}))}));return function(e){return n.apply(this,arguments)}}(),onError:function(e){var r;l.Am.error(null!==(r=(0,m.ic)(e.response))&&void 0!==r?r:"Login failed")}})},w=n(7536),v=function(){var e=(0,w.cI)(),r=e.register,n=e.handleSubmit,i=e.formState,o=i.errors,u=i.isSubmitting,c=b();return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,a.jsxs)("div",{className:"bg-white p-8 rounded shadow-md w-full max-w-md",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Inventory Management Login"}),(0,a.jsxs)("form",{onSubmit:n((function(e){c.mutate({email:e.email,password:e.password})})),className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,a.jsx)("input",(0,s.Z)((0,t.Z)({id:"email",type:"email",className:"mt-1 block w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},r("email",{required:"Email is required"})),{autoComplete:"username"})),o.email&&(0,a.jsx)("span",{className:"text-red-500 text-xs",children:o.email.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,a.jsx)("input",(0,s.Z)((0,t.Z)({id:"password",type:"password",className:"mt-1 block w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},r("password",{required:"Password is required"})),{autoComplete:"current-password"})),o.password&&(0,a.jsx)("span",{className:"text-red-500 text-xs",children:o.password.message})]}),c.isError&&(0,a.jsx)("div",{className:"text-red-500 text-sm",children:"Login failed. Please check your credentials."}),c.isSuccess&&(0,a.jsx)("div",{className:"text-green-600 text-sm",children:"Login successful!"}),(0,a.jsx)("button",{type:"submit",className:"w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition",disabled:u||c.isLoading,children:c.isLoading?"Logging in...":"Login"})]})]})})}}},function(e){e.O(0,[536,873,774,888,179],(function(){return r=3236,e(e.s=r);var r}));var r=e.O();_N_E=r}]);