{"help-one-title": "Can I use this Template For Next JS or TypeScript learning?", "help-one-content": "Yes, You can use this Template for your Learning process. The coding structure is made easy for our customers. ", "help-two-title": "How can I deploy BoroBazar Template?", "help-two-content": "We have made the deployment process clean and simple. You can deploy the Template easily by following our documentation", "help-three-title": "How can I get the customer support?", "help-three-content": "After purchasing the product please open a ticket in the support forum and our support guys are always ready to help you at any kind of case. Follow this link: https://redqsupport.ticksy.com/", "help-four-title": "Will you regularly give updates of BoroBazar Template?", "help-four-content": "Yes, We will update the BoroBazar Template Regularly. We have a plan to give more exciting demos to our customers on a regular basis.", "help-five-title": "How can I get the refund?", "help-five-content": "The refund is available when we have shown the feature on our demo however it is not available on our template..", "help-six-title": "Template installation failed, how to run the template? ", "help-six-content": "Please read the documentation carefully . We also have some online video tutorials regarding this issue . If the problem remains, Please Open a ticket in the support forum", "end": "end"}