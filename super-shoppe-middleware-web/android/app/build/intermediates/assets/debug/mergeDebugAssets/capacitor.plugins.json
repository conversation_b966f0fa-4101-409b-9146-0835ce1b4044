[{"pkg": "@capacitor-community/privacy-screen", "classpath": "com.getcapacitor.plugin.privacyscreen.PrivacyScreenPlugin"}, {"pkg": "@capacitor-community/screen-brightness", "classpath": "com.elylucas.capscreenbrightness.ScreenBrightnessPlugin"}, {"pkg": "@capacitor/app", "classpath": "com.capacitorjs.plugins.app.AppPlugin"}, {"pkg": "@capacitor/barcode-scanner", "classpath": "com.capacitorjs.barcodescanner.CapacitorBarcodeScannerPlugin"}, {"pkg": "@capacitor/browser", "classpath": "com.capacitorjs.plugins.browser.BrowserPlugin"}, {"pkg": "@capacitor/device", "classpath": "com.capacitorjs.plugins.device.DevicePlugin"}, {"pkg": "@capacitor/geolocation", "classpath": "com.capacitorjs.plugins.geolocation.GeolocationPlugin"}, {"pkg": "@capacitor/preferences", "classpath": "com.capacitorjs.plugins.preferences.PreferencesPlugin"}, {"pkg": "@capacitor/push-notifications", "classpath": "com.capacitorjs.plugins.pushnotifications.PushNotificationsPlugin"}, {"pkg": "@capacitor/toast", "classpath": "com.capacitorjs.plugins.toast.ToastPlugin"}]