{"logs": [{"outputFile": "my.supershoppe.app-mergeDebugResources-63:/values-be/values-be.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/res/values-be/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,454,575,681,832,954,1065,1165,1323,1426,1585,1709,1858,2013,2078,2136", "endColumns": "102,157,120,105,150,121,110,99,157,102,158,123,148,154,64,57,74", "endOffsets": "295,453,574,680,831,953,1064,1164,1322,1425,1584,1708,1857,2012,2077,2135,2210"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3743,3850,4012,4137,4247,4402,4528,4643,4893,5055,5162,5325,5453,5606,5765,5834,5896", "endColumns": "106,161,124,109,154,125,114,103,161,106,162,127,152,158,68,61,78", "endOffsets": "3845,4007,4132,4242,4397,4523,4638,4742,5050,5157,5320,5448,5601,5760,5829,5891,5970"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/res/values-be/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4747", "endColumns": "145", "endOffsets": "4888"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/ce7a7439fda0c36e77102a7d38ff3226/transformed/browser-1.8.0/res/values-be/values-be.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,272,384", "endColumns": "108,107,111,106", "endOffsets": "159,267,379,486"}, "to": {"startLines": "56,60,61,62", "startColumns": "4,4,4,4", "startOffsets": "5975,6367,6475,6587", "endColumns": "108,107,111,106", "endOffsets": "6079,6470,6582,6689"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/res/values-be/values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "29,30,31,32,33,34,35,131", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2835,2933,3035,3135,3236,3342,3445,13974", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "2928,3030,3130,3231,3337,3440,3561,14070"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/a8dee9b18ef2037d25f00c7473086895/transformed/ui-release/res/values-be/values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,376,479,565,645,734,822,904,987,1074,1146,1230,1308,1384,1469,1539", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,83,77,75,84,69,122", "endOffsets": "193,277,371,474,560,640,729,817,899,982,1069,1141,1225,1303,1379,1464,1534,1657"}, "to": {"startLines": "36,37,57,58,59,63,64,122,123,124,125,127,128,129,130,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3566,3659,6084,6178,6281,6694,6774,13242,13330,13412,13495,13664,13736,13820,13898,14075,14160,14230", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,83,77,75,84,69,122", "endOffsets": "3654,3738,6173,6276,6362,6769,6858,13325,13407,13490,13577,13731,13815,13893,13969,14155,14225,14348"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/84189f789b5e554413b94a7106c44443/transformed/material3-release/res/values-be/values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,406,523,621,718,832,955,1070,1215,1299,1410,1503,1600,1714,1837,1953,2100,2246,2384,2561,2693,2818,2947,3069,3163,3261,3387,3520,3619,3730,3839,3989,4142,4250,4350,4435,4530,4626,4744,4830,4917,5017,5104,5191,5291,5397,5493,5591,5680,5788,5884,5984,6130,6220,6338", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,117,85,86,99,86,86,99,105,95,97,88,107,95,99,145,89,117,95", "endOffsets": "167,282,401,518,616,713,827,950,1065,1210,1294,1405,1498,1595,1709,1832,1948,2095,2241,2379,2556,2688,2813,2942,3064,3158,3256,3382,3515,3614,3725,3834,3984,4137,4245,4345,4430,4525,4621,4739,4825,4912,5012,5099,5186,5286,5392,5488,5586,5675,5783,5879,5979,6125,6215,6333,6429"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6863,6980,7095,7214,7331,7429,7526,7640,7763,7878,8023,8107,8218,8311,8408,8522,8645,8761,8908,9054,9192,9369,9501,9626,9755,9877,9971,10069,10195,10328,10427,10538,10647,10797,10950,11058,11158,11243,11338,11434,11552,11638,11725,11825,11912,11999,12099,12205,12301,12399,12488,12596,12692,12792,12938,13028,13146", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,117,85,86,99,86,86,99,105,95,97,88,107,95,99,145,89,117,95", "endOffsets": "6975,7090,7209,7326,7424,7521,7635,7758,7873,8018,8102,8213,8306,8403,8517,8640,8756,8903,9049,9187,9364,9496,9621,9750,9872,9966,10064,10190,10323,10422,10533,10642,10792,10945,11053,11153,11238,11333,11429,11547,11633,11720,11820,11907,11994,12094,12200,12296,12394,12483,12591,12687,12787,12933,13023,13141,13237"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/80c866390182a769f8356a80ded4035a/transformed/foundation-release/res/values-be/values-be.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,102", "endOffsets": "137,240"}, "to": {"startLines": "135,136", "startColumns": "4,4", "startOffsets": "14353,14440", "endColumns": "86,102", "endOffsets": "14435,14538"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/dda665aa4a1576cfb1759fb2bbcd5279/transformed/appcompat-1.7.0/res/values-be/values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,13582", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,13659"}}]}]}