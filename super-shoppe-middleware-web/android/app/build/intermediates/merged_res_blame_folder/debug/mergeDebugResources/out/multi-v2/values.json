{"logs": [{"outputFile": "/Users/<USER>/.gradle/daemon/8.11.1/my.supershoppe.app-mergeDebugResources-34:/values/values.xml", "map": [{"source": "/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/res/values/strings.xml", "from": {"startLines": "2,5,4,3", "startColumns": "4,4,4,4", "startOffsets": "55,226,166,105", "endColumns": "49,64,59,60", "endOffsets": "100,286,221,161"}, "to": {"startLines": "333,360,366,369", "startColumns": "4,4,4,4", "startOffsets": "21931,24893,25335,25519", "endColumns": "49,64,59,60", "endOffsets": "21976,24953,25390,25575"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/75881b531e34911967ea794bd3408c30/transformed/coordinatorlayout-1.2.0/res/values/values.xml", "from": {"startLines": "2,3,6,16", "startColumns": "4,4,4,4", "startOffsets": "55,116,261,869", "endLines": "2,5,15,104", "endColumns": "60,12,24,24", "endOffsets": "111,256,864,6075"}, "to": {"startLines": "2,1932,2652,2658", "startColumns": "4,4,4,4", "startOffsets": "150,130786,155291,155502", "endLines": "2,1934,2657,2741", "endColumns": "60,12,24,24", "endOffsets": "206,130926,155497,160013"}}, {"source": "/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/android/capacitor/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "100,175,251,331", "endColumns": "74,75,79,79", "endOffsets": "170,246,326,406"}, "to": {"startLines": "43,44,45,365", "startColumns": "4,4,4,4", "startOffsets": "2974,3049,3125,25255", "endColumns": "74,75,79,79", "endOffsets": "3044,3120,3200,25330"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/ef234481c09f01fb9f0508a5da2b1126/transformed/core-splashscreen-1.0.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,32,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,174,243,315,378,450,524,600,676,753,824,893,964,1032,1113,1205,1298,1407,1528,1988,2763", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,31,44,48", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "110,169,238,310,373,445,519,595,671,748,819,888,959,1027,1108,1200,1293,1402,1523,1983,2758,3031"}, "to": {"startLines": "7,8,10,11,12,13,213,214,215,216,217,218,219,302,643,644,645,1475,1477,1797,1806,1819", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "420,480,580,649,721,784,14666,14740,14816,14892,14969,15040,15109,19776,43360,43441,43533,95970,96079,120480,120940,121715", "endLines": "7,8,10,11,12,13,213,214,215,216,217,218,219,302,643,644,645,1476,1478,1805,1818,1822", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "475,534,644,716,779,851,14735,14811,14887,14964,15035,15104,15175,19839,43436,43528,43621,96074,96195,120935,121710,121983"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/998c918bf96ae2f6a4f5c8c644413a6f/transformed/savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "295", "startColumns": "4", "startOffsets": "19365", "endColumns": "53", "endOffsets": "19414"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "364", "startColumns": "4", "startOffsets": "25173", "endColumns": "81", "endOffsets": "25250"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/7a4193c6fbbe5e128015b7f6283124c0/transformed/fragment-1.8.4/res/values/values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "267,274,297,2816,2821", "startColumns": "4,4,4,4,4", "startOffsets": "17961,18260,19469,163077,163247", "endLines": "267,274,297,2820,2824", "endColumns": "56,64,63,24,24", "endOffsets": "18013,18320,19528,163242,163391"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/ce7a7439fda0c36e77102a7d38ff3226/transformed/browser-1.8.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "35,36,37,38,180,181,359,361,362,363", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "2479,2537,2603,2666,12273,12344,24825,24958,25025,25104", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "2532,2598,2661,2723,12339,12411,24888,25020,25099,25168"}}, {"source": "/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/res/values/ic_launcher_background.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "56", "endOffsets": "107"}, "to": {"startLines": "64", "startColumns": "4", "startOffsets": "4557", "endColumns": "56", "endOffsets": "4609"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/dda665aa4a1576cfb1759fb2bbcd5279/transformed/appcompat-1.7.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "3,4,5,9,14,15,16,17,18,19,20,21,22,25,26,27,28,29,30,31,32,33,34,39,40,54,55,56,57,58,59,60,61,62,63,65,66,67,68,69,70,71,72,73,74,75,76,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,189,190,191,192,193,194,195,196,197,220,221,222,223,224,225,226,227,263,264,265,266,268,271,272,275,292,298,299,300,301,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,367,370,371,372,373,374,389,397,398,402,406,410,415,421,428,432,436,441,445,449,453,457,461,465,471,475,481,485,491,495,500,504,507,511,517,521,527,531,537,540,544,548,552,556,560,561,562,563,566,569,572,575,579,580,581,582,583,586,588,590,592,597,598,602,608,612,613,615,627,628,632,638,642,646,647,651,678,682,683,687,715,887,913,1084,1110,1141,1149,1155,1171,1193,1198,1203,1213,1222,1231,1235,1242,1261,1268,1269,1278,1281,1284,1288,1292,1296,1299,1300,1305,1310,1320,1325,1332,1338,1339,1342,1346,1351,1353,1355,1358,1361,1363,1367,1370,1377,1380,1383,1387,1389,1393,1395,1397,1399,1403,1411,1419,1431,1437,1446,1449,1460,1463,1464,1469,1470,1479,1548,1618,1619,1629,1638,1639,1641,1645,1648,1651,1654,1657,1660,1663,1666,1670,1673,1676,1679,1683,1686,1690,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1716,1718,1719,1720,1721,1722,1723,1724,1725,1727,1728,1730,1731,1733,1735,1736,1738,1739,1740,1741,1742,1743,1745,1746,1747,1748,1749,1761,1763,1765,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1781,1782,1783,1784,1785,1786,1787,1789,1793,1823,1824,1825,1826,1827,1828,1832,1833,1834,1835,1837,1839,1841,1843,1845,1846,1847,1848,1850,1852,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1868,1869,1870,1871,1873,1875,1876,1878,1879,1881,1883,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1898,1899,1900,1901,1903,1904,1905,1906,1907,1909,1911,1913,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1935,2010,2013,2016,2019,2033,2039,2081,2084,2113,2140,2149,2213,2576,2586,2624,2742,2864,2888,2894,2913,2934,3058,3078,3084,3088,3094,3148,3180,3246,3266,3321,3333,3359", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "211,266,311,539,856,911,973,1037,1107,1168,1243,1319,1396,1634,1719,1801,1877,1953,2030,2108,2214,2320,2399,2728,2785,3876,3950,4025,4090,4156,4216,4277,4349,4422,4489,4614,4673,4732,4791,4850,4909,4963,5017,5070,5124,5178,5232,5418,5492,5571,5644,5718,5789,5861,5933,6006,6063,6121,6194,6268,6342,6417,6489,6562,6632,6703,6763,6824,6893,6962,7032,7106,7182,7246,7323,7399,7476,7541,7610,7687,7762,7831,7899,7976,8042,8103,8200,8265,8334,8433,8504,8563,8621,8678,8737,8801,8872,8944,9016,9088,9160,9227,9295,9363,9422,9485,9549,9639,9730,9790,9856,9923,9989,10059,10123,10176,10243,10304,10371,10484,10542,10605,10670,10735,10810,10883,10955,10999,11046,11092,11141,11202,11263,11324,11386,11450,11514,11578,11643,11706,11766,11827,11893,11952,12012,12074,12145,12205,12904,12990,13077,13167,13254,13342,13424,13507,13597,15180,15232,15290,15335,15401,15465,15522,15579,17756,17813,17861,17910,18018,18122,18169,18325,19230,19533,19597,19659,19719,19982,20056,20126,20204,20258,20328,20413,20461,20507,20568,20631,20697,20761,20832,20895,20960,21024,21085,21146,21198,21271,21345,21414,21489,21563,21637,21778,25395,25580,25658,25748,25836,25932,26706,27288,27377,27624,27905,28157,28442,28835,29312,29534,29756,30032,30259,30489,30719,30949,31179,31406,31825,32051,32476,32706,33134,33353,33636,33844,33975,34202,34628,34853,35280,35501,35926,36046,36322,36623,36947,37238,37552,37689,37820,37925,38167,38334,38538,38746,39017,39129,39241,39346,39463,39677,39823,39963,40049,40397,40485,40731,41149,41398,41480,41578,42235,42335,42587,43011,43266,43626,43715,43952,45976,46218,46320,46573,48729,59410,60926,71621,73149,74906,75532,75952,77213,78478,78734,78970,79517,80011,80616,80814,81394,82762,83137,83255,83793,83950,84146,84419,84675,84845,84986,85050,85415,85782,86458,86722,87060,87413,87507,87693,87999,88261,88386,88513,88752,88963,89082,89275,89452,89907,90088,90210,90469,90582,90769,90871,90978,91107,91382,91890,92386,93263,93557,94127,94276,95008,95180,95264,95600,95692,96200,101431,106802,106864,107442,108026,108117,108230,108459,108619,108771,108942,109108,109277,109444,109607,109850,110020,110193,110364,110638,110837,111042,111372,111456,111552,111648,111746,111846,111948,112050,112152,112254,112356,112456,112552,112664,112793,112916,113047,113178,113276,113390,113484,113624,113758,113854,113966,114066,114182,114278,114390,114490,114630,114766,114930,115060,115218,115368,115509,115653,115788,115900,116050,116178,116306,116442,116574,116704,116834,116946,117844,117990,118134,118272,118338,118428,118504,118608,118698,118800,118908,119016,119116,119196,119288,119386,119496,119548,119626,119732,119824,119928,120038,120160,120323,121988,122068,122168,122258,122368,122458,122699,122793,122899,122991,123091,123203,123317,123433,123549,123643,123757,123869,123971,124091,124213,124295,124399,124519,124645,124743,124837,124925,125037,125153,125275,125387,125562,125678,125764,125856,125968,126092,126159,126285,126353,126481,126625,126753,126822,126917,127032,127145,127244,127353,127464,127575,127676,127781,127881,128011,128102,128225,128319,128431,128517,128621,128717,128805,128923,129027,129131,129257,129345,129453,129553,129643,129753,129837,129939,130023,130077,130141,130247,130333,130443,130527,130931,133547,133665,133780,133860,134221,134454,135858,135936,137280,138641,139029,141872,151925,152263,153934,160018,164245,164996,165258,165773,166152,170430,171036,171265,171416,171631,173131,173981,177007,177751,179882,180222,181533", "endLines": "3,4,5,9,14,15,16,17,18,19,20,21,22,25,26,27,28,29,30,31,32,33,34,39,40,54,55,56,57,58,59,60,61,62,63,65,66,67,68,69,70,71,72,73,74,75,76,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,189,190,191,192,193,194,195,196,197,220,221,222,223,224,225,226,227,263,264,265,266,268,271,272,275,292,298,299,300,301,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,367,370,371,372,373,374,396,397,401,405,409,414,420,427,431,435,440,444,448,452,456,460,464,470,474,480,484,490,494,499,503,506,510,516,520,526,530,536,539,543,547,551,555,559,560,561,562,565,568,571,574,578,579,580,581,582,585,587,589,591,596,597,601,607,611,612,614,626,627,631,637,641,642,646,650,677,681,682,686,714,886,912,1083,1109,1140,1148,1154,1170,1192,1197,1202,1212,1221,1230,1234,1241,1260,1267,1268,1277,1280,1283,1287,1291,1295,1298,1299,1304,1309,1319,1324,1331,1337,1338,1341,1345,1350,1352,1354,1357,1360,1362,1366,1369,1376,1379,1382,1386,1388,1392,1394,1396,1398,1402,1410,1418,1430,1436,1445,1448,1459,1462,1463,1468,1469,1474,1547,1617,1618,1628,1637,1638,1640,1644,1647,1650,1653,1656,1659,1662,1665,1669,1672,1675,1678,1682,1685,1689,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1715,1717,1718,1719,1720,1721,1722,1723,1724,1726,1727,1729,1730,1732,1734,1735,1737,1738,1739,1740,1741,1742,1744,1745,1746,1747,1748,1749,1762,1764,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1780,1781,1782,1783,1784,1785,1786,1788,1792,1796,1823,1824,1825,1826,1827,1831,1832,1833,1834,1836,1838,1840,1842,1844,1845,1846,1847,1849,1851,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1867,1868,1869,1870,1872,1874,1875,1877,1878,1880,1882,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1897,1898,1899,1900,1902,1903,1904,1905,1906,1908,1910,1912,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,2009,2012,2015,2018,2032,2038,2048,2083,2112,2139,2148,2212,2575,2579,2613,2651,2759,2887,2893,2899,2933,3057,3077,3083,3087,3093,3128,3159,3245,3265,3320,3332,3358,3365", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "261,306,355,575,906,968,1032,1102,1163,1238,1314,1391,1469,1714,1796,1872,1948,2025,2103,2209,2315,2394,2474,2780,2838,3945,4020,4085,4151,4211,4272,4344,4417,4484,4552,4668,4727,4786,4845,4904,4958,5012,5065,5119,5173,5227,5281,5487,5566,5639,5713,5784,5856,5928,6001,6058,6116,6189,6263,6337,6412,6484,6557,6627,6698,6758,6819,6888,6957,7027,7101,7177,7241,7318,7394,7471,7536,7605,7682,7757,7826,7894,7971,8037,8098,8195,8260,8329,8428,8499,8558,8616,8673,8732,8796,8867,8939,9011,9083,9155,9222,9290,9358,9417,9480,9544,9634,9725,9785,9851,9918,9984,10054,10118,10171,10238,10299,10366,10479,10537,10600,10665,10730,10805,10878,10950,10994,11041,11087,11136,11197,11258,11319,11381,11445,11509,11573,11638,11701,11761,11822,11888,11947,12007,12069,12140,12200,12268,12985,13072,13162,13249,13337,13419,13502,13592,13683,15227,15285,15330,15396,15460,15517,15574,15628,17808,17856,17905,17956,18047,18164,18213,18366,19257,19592,19654,19714,19771,20051,20121,20199,20253,20323,20408,20456,20502,20563,20626,20692,20756,20827,20890,20955,21019,21080,21141,21193,21266,21340,21409,21484,21558,21632,21773,21843,25443,25653,25743,25831,25927,26017,27283,27372,27619,27900,28152,28437,28830,29307,29529,29751,30027,30254,30484,30714,30944,31174,31401,31820,32046,32471,32701,33129,33348,33631,33839,33970,34197,34623,34848,35275,35496,35921,36041,36317,36618,36942,37233,37547,37684,37815,37920,38162,38329,38533,38741,39012,39124,39236,39341,39458,39672,39818,39958,40044,40392,40480,40726,41144,41393,41475,41573,42230,42330,42582,43006,43261,43355,43710,43947,45971,46213,46315,46568,48724,59405,60921,71616,73144,74901,75527,75947,77208,78473,78729,78965,79512,80006,80611,80809,81389,82757,83132,83250,83788,83945,84141,84414,84670,84840,84981,85045,85410,85777,86453,86717,87055,87408,87502,87688,87994,88256,88381,88508,88747,88958,89077,89270,89447,89902,90083,90205,90464,90577,90764,90866,90973,91102,91377,91885,92381,93258,93552,94122,94271,95003,95175,95259,95595,95687,95965,101426,106797,106859,107437,108021,108112,108225,108454,108614,108766,108937,109103,109272,109439,109602,109845,110015,110188,110359,110633,110832,111037,111367,111451,111547,111643,111741,111841,111943,112045,112147,112249,112351,112451,112547,112659,112788,112911,113042,113173,113271,113385,113479,113619,113753,113849,113961,114061,114177,114273,114385,114485,114625,114761,114925,115055,115213,115363,115504,115648,115783,115895,116045,116173,116301,116437,116569,116699,116829,116941,117081,117985,118129,118267,118333,118423,118499,118603,118693,118795,118903,119011,119111,119191,119283,119381,119491,119543,119621,119727,119819,119923,120033,120155,120318,120475,122063,122163,122253,122363,122453,122694,122788,122894,122986,123086,123198,123312,123428,123544,123638,123752,123864,123966,124086,124208,124290,124394,124514,124640,124738,124832,124920,125032,125148,125270,125382,125557,125673,125759,125851,125963,126087,126154,126280,126348,126476,126620,126748,126817,126912,127027,127140,127239,127348,127459,127570,127671,127776,127876,128006,128097,128220,128314,128426,128512,128616,128712,128800,128918,129022,129126,129252,129340,129448,129548,129638,129748,129832,129934,130018,130072,130136,130242,130328,130438,130522,130642,133542,133660,133775,133855,134216,134449,134966,135931,137275,138636,139024,141867,151920,152055,153628,155286,160585,164991,165253,165453,166147,170425,171031,171260,171411,171626,172709,173438,177002,177746,179877,180217,181528,181731"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/f212eb2fcec7b76a8049b85cea08416b/transformed/lifecycle-runtime-2.6.2/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "293", "startColumns": "4", "startOffsets": "19262", "endColumns": "42", "endOffsets": "19300"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/16884767c054ac4cab0f70a5a4855d4d/transformed/appcompat-resources-1.7.0/res/values/values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2049,2065,2071,3160,3176", "startColumns": "4,4,4,4,4", "startOffsets": "134971,135396,135574,173443,173854", "endLines": "2064,2070,2080,3175,3179", "endColumns": "24,24,24,24,24", "endOffsets": "135391,135569,135853,173849,173976"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/d8c531c2afb462bf72eed5fcccc5521b/transformed/activity-1.9.3/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "273,294", "startColumns": "4,4", "startOffsets": "18218,19305", "endColumns": "41,59", "endOffsets": "18255,19360"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,157,178,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8915,9596,10278", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,156,177,210,216", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8910,9591,10273,10440"}, "to": {"startLines": "6,23,24,41,42,77,78,182,183,184,185,186,187,188,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,269,270,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,304,334,335,336,337,338,339,340,368,1750,1751,1755,1756,1760,1930,1931,2580,2614,2760,2795,2825,2858", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "360,1474,1546,2843,2908,5286,5355,12416,12486,12554,12626,12696,12757,12831,13688,13749,13810,13872,13936,13998,14059,14127,14227,14287,14353,14426,14495,14552,14604,15633,15705,15781,15846,15905,15964,16024,16084,16144,16204,16264,16324,16384,16444,16504,16564,16623,16683,16743,16803,16863,16923,16983,17043,17103,17163,17223,17282,17342,17402,17461,17520,17579,17638,17697,18052,18087,18371,18426,18489,18544,18602,18660,18721,18784,18841,18892,18942,19003,19060,19126,19160,19195,19912,21981,22048,22120,22189,22258,22332,22404,25448,117086,117203,117404,117514,117715,130647,130719,152060,153633,160590,162396,163396,164078", "endLines": "6,23,24,41,42,77,78,182,183,184,185,186,187,188,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,269,270,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,304,334,335,336,337,338,339,340,368,1750,1754,1755,1759,1760,1930,1931,2585,2623,2794,2815,2857,2863", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "415,1541,1629,2903,2969,5350,5413,12481,12549,12621,12691,12752,12826,12899,13744,13805,13867,13931,13993,14054,14122,14222,14282,14348,14421,14490,14547,14599,14661,15700,15776,15841,15900,15959,16019,16079,16139,16199,16259,16319,16379,16439,16499,16559,16618,16678,16738,16798,16858,16918,16978,17038,17098,17158,17218,17277,17337,17397,17456,17515,17574,17633,17692,17751,18082,18117,18421,18484,18539,18597,18655,18716,18779,18836,18887,18937,18998,19055,19121,19155,19190,19225,19977,22043,22115,22184,22253,22327,22399,22487,25514,117198,117399,117509,117710,117839,130714,130781,152258,153929,162391,163072,164073,164240"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/5a75dca28172537968edb11f4713fc67/transformed/lifecycle-viewmodel-2.6.2/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "296", "startColumns": "4", "startOffsets": "19419", "endColumns": "49", "endOffsets": "19464"}}, {"source": "/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/res/values/styles.xml", "from": {"startLines": "4,11,18", "startColumns": "4,4,4", "startOffsets": "93,413,664", "endLines": "9,15,20", "endColumns": "12,12,12", "endOffsets": "407,657,810"}, "to": {"startLines": "375,381,386", "startColumns": "4,4,4", "startOffsets": "26022,26306,26555", "endLines": "380,385,388", "endColumns": "12,12,12", "endOffsets": "26301,26550,26701"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/res/values/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "46,47,48,49,50,51,52,53,341,342,343,344,345,346,347,348,350,351,352,353,354,355,356,357,358,2900,3129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3205,3295,3375,3465,3555,3635,3716,3796,22492,22597,22778,22903,23010,23190,23313,23429,23699,23887,23992,24173,24298,24473,24621,24684,24746,165458,172714", "endLines": "46,47,48,49,50,51,52,53,341,342,343,344,345,346,347,348,350,351,352,353,354,355,356,357,358,2912,3147", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "3290,3370,3460,3550,3630,3711,3791,3871,22592,22773,22898,23005,23185,23308,23424,23527,23882,23987,24168,24293,24468,24616,24679,24741,24820,165768,173126"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/0c35e3b0c2fe34519a603108fedf6f64/transformed/startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "332", "startColumns": "4", "startOffsets": "21848", "endColumns": "82", "endOffsets": "21926"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/res/values/values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "303,349", "startColumns": "4,4", "startOffsets": "19844,23532", "endColumns": "67,166", "endOffsets": "19907,23694"}}]}, {"outputFile": "my.supershoppe.app-mergeDebugResources-34:/values/values.xml", "map": [{"source": "/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/res/values/strings.xml", "from": {"startLines": "2,5,4,3", "startColumns": "4,4,4,4", "startOffsets": "55,259,188,116", "endColumns": "60,75,70,71", "endOffsets": "111,330,254,183"}, "to": {"startLines": "333,360,366,369", "startColumns": "4,4,4,4", "startOffsets": "21931,24904,25357,25552", "endColumns": "60,75,70,71", "endOffsets": "21987,24975,25423,25619"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/75881b531e34911967ea794bd3408c30/transformed/coordinatorlayout-1.2.0/res/values/values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2,1932", "startColumns": "4,4", "startOffsets": "150,130830", "endLines": "2,1934", "endColumns": "60,12", "endOffsets": "206,130970"}}, {"source": "/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/android/capacitor/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "43,44,45,365", "startColumns": "4,4,4,4", "startOffsets": "2974,3049,3125,25277", "endColumns": "74,75,79,79", "endOffsets": "3044,3120,3200,25352"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/ef234481c09f01fb9f0508a5da2b1126/transformed/core-splashscreen-1.0.1/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "7,8,10,11,12,13,213,214,215,216,217,218,219,302,643,644,645,1475,1477,1797,1806,1819", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "420,480,580,649,721,784,14666,14740,14816,14892,14969,15040,15109,19776,43404,43485,43577,96014,96123,120524,120984,121759", "endLines": "7,8,10,11,12,13,213,214,215,216,217,218,219,302,643,644,645,1476,1478,1805,1818,1822", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "475,534,644,716,779,851,14735,14811,14887,14964,15035,15104,15175,19839,43480,43572,43665,96118,96239,120979,121754,122027"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/998c918bf96ae2f6a4f5c8c644413a6f/transformed/savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "295", "startColumns": "4", "startOffsets": "19365", "endColumns": "53", "endOffsets": "19414"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "364", "startColumns": "4", "startOffsets": "25195", "endColumns": "81", "endOffsets": "25272"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/7a4193c6fbbe5e128015b7f6283124c0/transformed/fragment-1.8.4/res/values/values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "267,274,297", "startColumns": "4,4,4", "startOffsets": "17961,18260,19469", "endColumns": "56,64,63", "endOffsets": "18013,18320,19528"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/ce7a7439fda0c36e77102a7d38ff3226/transformed/browser-1.8.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "35,36,37,38,180,181,359,361,362,363", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "2479,2537,2603,2666,12273,12344,24836,24980,25047,25126", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "2532,2598,2661,2723,12339,12411,24899,25042,25121,25190"}}, {"source": "/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/res/values/ic_launcher_background.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "64", "startColumns": "4", "startOffsets": "4557", "endColumns": "56", "endOffsets": "4609"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/dda665aa4a1576cfb1759fb2bbcd5279/transformed/appcompat-1.7.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "3,4,5,9,14,15,16,17,18,19,20,21,22,25,26,27,28,29,30,31,32,33,34,39,40,54,55,56,57,58,59,60,61,62,63,65,66,67,68,69,70,71,72,73,74,75,76,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,189,190,191,192,193,194,195,196,197,220,221,222,223,224,225,226,227,263,264,265,266,268,271,272,275,292,298,299,300,301,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,367,370,371,372,373,374,389,397,398,402,406,410,415,421,428,432,436,441,445,449,453,457,461,465,471,475,481,485,491,495,500,504,507,511,517,521,527,531,537,540,544,548,552,556,560,561,562,563,566,569,572,575,579,580,581,582,583,586,588,590,592,597,598,602,608,612,613,615,627,628,632,638,642,646,647,651,678,682,683,687,715,887,913,1084,1110,1141,1149,1155,1171,1193,1198,1203,1213,1222,1231,1235,1242,1261,1268,1269,1278,1281,1284,1288,1292,1296,1299,1300,1305,1310,1320,1325,1332,1338,1339,1342,1346,1351,1353,1355,1358,1361,1363,1367,1370,1377,1380,1383,1387,1389,1393,1395,1397,1399,1403,1411,1419,1431,1437,1446,1449,1460,1463,1464,1469,1470,1479,1548,1618,1619,1629,1638,1639,1641,1645,1648,1651,1654,1657,1660,1663,1666,1670,1673,1676,1679,1683,1686,1690,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1716,1718,1719,1720,1721,1722,1723,1724,1725,1727,1728,1730,1731,1733,1735,1736,1738,1739,1740,1741,1742,1743,1745,1746,1747,1748,1749,1761,1763,1765,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1781,1782,1783,1784,1785,1786,1787,1789,1793,1823,1824,1825,1826,1827,1828,1832,1833,1834,1835,1837,1839,1841,1843,1845,1846,1847,1848,1850,1852,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1868,1869,1870,1871,1873,1875,1876,1878,1879,1881,1883,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1898,1899,1900,1901,1903,1904,1905,1906,1907,1909,1911,1913,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "211,266,311,539,856,911,973,1037,1107,1168,1243,1319,1396,1634,1719,1801,1877,1953,2030,2108,2214,2320,2399,2728,2785,3876,3950,4025,4090,4156,4216,4277,4349,4422,4489,4614,4673,4732,4791,4850,4909,4963,5017,5070,5124,5178,5232,5418,5492,5571,5644,5718,5789,5861,5933,6006,6063,6121,6194,6268,6342,6417,6489,6562,6632,6703,6763,6824,6893,6962,7032,7106,7182,7246,7323,7399,7476,7541,7610,7687,7762,7831,7899,7976,8042,8103,8200,8265,8334,8433,8504,8563,8621,8678,8737,8801,8872,8944,9016,9088,9160,9227,9295,9363,9422,9485,9549,9639,9730,9790,9856,9923,9989,10059,10123,10176,10243,10304,10371,10484,10542,10605,10670,10735,10810,10883,10955,10999,11046,11092,11141,11202,11263,11324,11386,11450,11514,11578,11643,11706,11766,11827,11893,11952,12012,12074,12145,12205,12904,12990,13077,13167,13254,13342,13424,13507,13597,15180,15232,15290,15335,15401,15465,15522,15579,17756,17813,17861,17910,18018,18122,18169,18325,19230,19533,19597,19659,19719,19982,20056,20126,20204,20258,20328,20413,20461,20507,20568,20631,20697,20761,20832,20895,20960,21024,21085,21146,21198,21271,21345,21414,21489,21563,21637,21778,25428,25624,25702,25792,25880,25976,26750,27332,27421,27668,27949,28201,28486,28879,29356,29578,29800,30076,30303,30533,30763,30993,31223,31450,31869,32095,32520,32750,33178,33397,33680,33888,34019,34246,34672,34897,35324,35545,35970,36090,36366,36667,36991,37282,37596,37733,37864,37969,38211,38378,38582,38790,39061,39173,39285,39390,39507,39721,39867,40007,40093,40441,40529,40775,41193,41442,41524,41622,42279,42379,42631,43055,43310,43670,43759,43996,46020,46262,46364,46617,48773,59454,60970,71665,73193,74950,75576,75996,77257,78522,78778,79014,79561,80055,80660,80858,81438,82806,83181,83299,83837,83994,84190,84463,84719,84889,85030,85094,85459,85826,86502,86766,87104,87457,87551,87737,88043,88305,88430,88557,88796,89007,89126,89319,89496,89951,90132,90254,90513,90626,90813,90915,91022,91151,91426,91934,92430,93307,93601,94171,94320,95052,95224,95308,95644,95736,96244,101475,106846,106908,107486,108070,108161,108274,108503,108663,108815,108986,109152,109321,109488,109651,109894,110064,110237,110408,110682,110881,111086,111416,111500,111596,111692,111790,111890,111992,112094,112196,112298,112400,112500,112596,112708,112837,112960,113091,113222,113320,113434,113528,113668,113802,113898,114010,114110,114226,114322,114434,114534,114674,114810,114974,115104,115262,115412,115553,115697,115832,115944,116094,116222,116350,116486,116618,116748,116878,116990,117888,118034,118178,118316,118382,118472,118548,118652,118742,118844,118952,119060,119160,119240,119332,119430,119540,119592,119670,119776,119868,119972,120082,120204,120367,122032,122112,122212,122302,122412,122502,122743,122837,122943,123035,123135,123247,123361,123477,123593,123687,123801,123913,124015,124135,124257,124339,124443,124563,124689,124787,124881,124969,125081,125197,125319,125431,125606,125722,125808,125900,126012,126136,126203,126329,126397,126525,126669,126797,126866,126961,127076,127189,127288,127397,127508,127619,127720,127825,127925,128055,128146,128269,128363,128475,128561,128665,128761,128849,128967,129071,129175,129301,129389,129497,129597,129687,129797,129881,129983,130067,130121,130185,130291,130377,130487,130571", "endLines": "3,4,5,9,14,15,16,17,18,19,20,21,22,25,26,27,28,29,30,31,32,33,34,39,40,54,55,56,57,58,59,60,61,62,63,65,66,67,68,69,70,71,72,73,74,75,76,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,189,190,191,192,193,194,195,196,197,220,221,222,223,224,225,226,227,263,264,265,266,268,271,272,275,292,298,299,300,301,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,367,370,371,372,373,374,396,397,401,405,409,414,420,427,431,435,440,444,448,452,456,460,464,470,474,480,484,490,494,499,503,506,510,516,520,526,530,536,539,543,547,551,555,559,560,561,562,565,568,571,574,578,579,580,581,582,585,587,589,591,596,597,601,607,611,612,614,626,627,631,637,641,642,646,650,677,681,682,686,714,886,912,1083,1109,1140,1148,1154,1170,1192,1197,1202,1212,1221,1230,1234,1241,1260,1267,1268,1277,1280,1283,1287,1291,1295,1298,1299,1304,1309,1319,1324,1331,1337,1338,1341,1345,1350,1352,1354,1357,1360,1362,1366,1369,1376,1379,1382,1386,1388,1392,1394,1396,1398,1402,1410,1418,1430,1436,1445,1448,1459,1462,1463,1468,1469,1474,1547,1617,1618,1628,1637,1638,1640,1644,1647,1650,1653,1656,1659,1662,1665,1669,1672,1675,1678,1682,1685,1689,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1715,1717,1718,1719,1720,1721,1722,1723,1724,1726,1727,1729,1730,1732,1734,1735,1737,1738,1739,1740,1741,1742,1744,1745,1746,1747,1748,1749,1762,1764,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1780,1781,1782,1783,1784,1785,1786,1788,1792,1796,1823,1824,1825,1826,1827,1831,1832,1833,1834,1836,1838,1840,1842,1844,1845,1846,1847,1849,1851,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1867,1868,1869,1870,1872,1874,1875,1877,1878,1880,1882,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1897,1898,1899,1900,1902,1903,1904,1905,1906,1908,1910,1912,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "261,306,355,575,906,968,1032,1102,1163,1238,1314,1391,1469,1714,1796,1872,1948,2025,2103,2209,2315,2394,2474,2780,2838,3945,4020,4085,4151,4211,4272,4344,4417,4484,4552,4668,4727,4786,4845,4904,4958,5012,5065,5119,5173,5227,5281,5487,5566,5639,5713,5784,5856,5928,6001,6058,6116,6189,6263,6337,6412,6484,6557,6627,6698,6758,6819,6888,6957,7027,7101,7177,7241,7318,7394,7471,7536,7605,7682,7757,7826,7894,7971,8037,8098,8195,8260,8329,8428,8499,8558,8616,8673,8732,8796,8867,8939,9011,9083,9155,9222,9290,9358,9417,9480,9544,9634,9725,9785,9851,9918,9984,10054,10118,10171,10238,10299,10366,10479,10537,10600,10665,10730,10805,10878,10950,10994,11041,11087,11136,11197,11258,11319,11381,11445,11509,11573,11638,11701,11761,11822,11888,11947,12007,12069,12140,12200,12268,12985,13072,13162,13249,13337,13419,13502,13592,13683,15227,15285,15330,15396,15460,15517,15574,15628,17808,17856,17905,17956,18047,18164,18213,18366,19257,19592,19654,19714,19771,20051,20121,20199,20253,20323,20408,20456,20502,20563,20626,20692,20756,20827,20890,20955,21019,21080,21141,21193,21266,21340,21409,21484,21558,21632,21773,21843,25476,25697,25787,25875,25971,26061,27327,27416,27663,27944,28196,28481,28874,29351,29573,29795,30071,30298,30528,30758,30988,31218,31445,31864,32090,32515,32745,33173,33392,33675,33883,34014,34241,34667,34892,35319,35540,35965,36085,36361,36662,36986,37277,37591,37728,37859,37964,38206,38373,38577,38785,39056,39168,39280,39385,39502,39716,39862,40002,40088,40436,40524,40770,41188,41437,41519,41617,42274,42374,42626,43050,43305,43399,43754,43991,46015,46257,46359,46612,48768,59449,60965,71660,73188,74945,75571,75991,77252,78517,78773,79009,79556,80050,80655,80853,81433,82801,83176,83294,83832,83989,84185,84458,84714,84884,85025,85089,85454,85821,86497,86761,87099,87452,87546,87732,88038,88300,88425,88552,88791,89002,89121,89314,89491,89946,90127,90249,90508,90621,90808,90910,91017,91146,91421,91929,92425,93302,93596,94166,94315,95047,95219,95303,95639,95731,96009,101470,106841,106903,107481,108065,108156,108269,108498,108658,108810,108981,109147,109316,109483,109646,109889,110059,110232,110403,110677,110876,111081,111411,111495,111591,111687,111785,111885,111987,112089,112191,112293,112395,112495,112591,112703,112832,112955,113086,113217,113315,113429,113523,113663,113797,113893,114005,114105,114221,114317,114429,114529,114669,114805,114969,115099,115257,115407,115548,115692,115827,115939,116089,116217,116345,116481,116613,116743,116873,116985,117125,118029,118173,118311,118377,118467,118543,118647,118737,118839,118947,119055,119155,119235,119327,119425,119535,119587,119665,119771,119863,119967,120077,120199,120362,120519,122107,122207,122297,122407,122497,122738,122832,122938,123030,123130,123242,123356,123472,123588,123682,123796,123908,124010,124130,124252,124334,124438,124558,124684,124782,124876,124964,125076,125192,125314,125426,125601,125717,125803,125895,126007,126131,126198,126324,126392,126520,126664,126792,126861,126956,127071,127184,127283,127392,127503,127614,127715,127820,127920,128050,128141,128264,128358,128470,128556,128660,128756,128844,128962,129066,129170,129296,129384,129492,129592,129682,129792,129876,129978,130062,130116,130180,130286,130372,130482,130566,130686"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/f212eb2fcec7b76a8049b85cea08416b/transformed/lifecycle-runtime-2.6.2/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "293", "startColumns": "4", "startOffsets": "19262", "endColumns": "42", "endOffsets": "19300"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/d8c531c2afb462bf72eed5fcccc5521b/transformed/activity-1.9.3/res/values/values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "273,294", "startColumns": "4,4", "startOffsets": "18218,19305", "endColumns": "41,59", "endOffsets": "18255,19360"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "6,23,24,41,42,77,78,182,183,184,185,186,187,188,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,269,270,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,304,334,335,336,337,338,339,340,368,1750,1751,1755,1756,1760,1930,1931", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "360,1474,1546,2843,2908,5286,5355,12416,12486,12554,12626,12696,12757,12831,13688,13749,13810,13872,13936,13998,14059,14127,14227,14287,14353,14426,14495,14552,14604,15633,15705,15781,15846,15905,15964,16024,16084,16144,16204,16264,16324,16384,16444,16504,16564,16623,16683,16743,16803,16863,16923,16983,17043,17103,17163,17223,17282,17342,17402,17461,17520,17579,17638,17697,18052,18087,18371,18426,18489,18544,18602,18660,18721,18784,18841,18892,18942,19003,19060,19126,19160,19195,19912,21992,22059,22131,22200,22269,22343,22415,25481,117130,117247,117448,117558,117759,130691,130763", "endLines": "6,23,24,41,42,77,78,182,183,184,185,186,187,188,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,269,270,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,304,334,335,336,337,338,339,340,368,1750,1754,1755,1759,1760,1930,1931", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "415,1541,1629,2903,2969,5350,5413,12481,12549,12621,12691,12752,12826,12899,13744,13805,13867,13931,13993,14054,14122,14222,14282,14348,14421,14490,14547,14599,14661,15700,15776,15841,15900,15959,16019,16079,16139,16199,16259,16319,16379,16439,16499,16559,16618,16678,16738,16798,16858,16918,16978,17038,17098,17158,17218,17277,17337,17397,17456,17515,17574,17633,17692,17751,18082,18117,18421,18484,18539,18597,18655,18716,18779,18836,18887,18937,18998,19055,19121,19155,19190,19225,19977,22054,22126,22195,22264,22338,22410,22498,25547,117242,117443,117553,117754,117883,130758,130825"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/5a75dca28172537968edb11f4713fc67/transformed/lifecycle-viewmodel-2.6.2/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "296", "startColumns": "4", "startOffsets": "19419", "endColumns": "49", "endOffsets": "19464"}}, {"source": "/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/res/values/styles.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "375,381,386", "startColumns": "4,4,4", "startOffsets": "26066,26350,26599", "endLines": "380,385,388", "endColumns": "12,12,12", "endOffsets": "26345,26594,26745"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "46,47,48,49,50,51,52,53,341,342,343,344,345,346,347,348,350,351,352,353,354,355,356,357,358", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3205,3295,3375,3465,3555,3635,3716,3796,22503,22608,22789,22914,23021,23201,23324,23440,23710,23898,24003,24184,24309,24484,24632,24695,24757", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "3290,3370,3460,3550,3630,3711,3791,3871,22603,22784,22909,23016,23196,23319,23435,23538,23893,23998,24179,24304,24479,24627,24690,24752,24831"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/0c35e3b0c2fe34519a603108fedf6f64/transformed/startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "332", "startColumns": "4", "startOffsets": "21848", "endColumns": "82", "endOffsets": "21926"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/res/values/values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "303,349", "startColumns": "4,4", "startOffsets": "19844,23543", "endColumns": "67,166", "endOffsets": "19907,23705"}}]}]}