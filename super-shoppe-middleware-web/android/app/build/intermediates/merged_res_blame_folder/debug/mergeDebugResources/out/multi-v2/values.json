{"logs": [{"outputFile": "my.supershoppe.app-mergeDebugResources-63:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/75881b531e34911967ea794bd3408c30/transformed/coordinatorlayout-1.2.0/res/values/values.xml", "from": {"startLines": "2,3,6,16", "startColumns": "4,4,4,4", "startOffsets": "55,116,261,869", "endLines": "2,5,15,104", "endColumns": "60,12,24,24", "endOffsets": "111,256,864,6075"}, "to": {"startLines": "2,2067,2797,2803", "startColumns": "4,4,4,4", "startOffsets": "150,139152,163983,164194", "endLines": "2,2069,2802,2886", "endColumns": "60,12,24,24", "endOffsets": "206,139292,164189,168705"}}, {"source": "/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/android/capacitor/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "100,175,251,331", "endColumns": "74,75,79,79", "endOffsets": "170,246,326,406"}, "to": {"startLines": "63,64,65,465", "startColumns": "4,4,4,4", "startOffsets": "3769,3844,3920,31697", "endColumns": "74,75,79,79", "endOffsets": "3839,3915,3995,31772"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/ef234481c09f01fb9f0508a5da2b1126/transformed/core-splashscreen-1.0.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,32,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,174,243,315,378,450,524,600,676,753,824,893,964,1032,1113,1205,1298,1407,1528,1988,2763", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,31,44,48", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "110,169,238,310,373,445,519,595,671,748,819,888,959,1027,1108,1200,1293,1402,1523,1983,2758,3031"}, "to": {"startLines": "11,20,30,31,32,33,233,234,235,236,237,238,239,332,755,756,757,1587,1589,1932,1941,1954", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "572,939,1375,1444,1516,1579,15461,15535,15611,15687,15764,15835,15904,21164,50440,50521,50613,103050,103159,128846,129306,130081", "endLines": "11,20,30,31,32,33,233,234,235,236,237,238,239,332,755,756,757,1588,1590,1940,1953,1957", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "627,993,1439,1511,1574,1646,15530,15606,15682,15759,15830,15899,15970,21227,50516,50608,50701,103154,103275,129301,130076,130349"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/a8dee9b18ef2037d25f00c7473086895/transformed/ui-release/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,287,289,290,293,295,327,372,373,394,395,396,401,402,464,466,468,469,471,472,473,474,476,477,478,1591,1607,1610", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16641,16700,16759,16819,16879,16939,16999,17059,17119,17179,17239,17299,17359,17418,17478,17538,17598,17658,17718,17778,17838,17898,17958,18018,18077,18137,18197,18256,18315,18374,18433,18492,18756,18890,18948,19126,19211,20868,23964,24029,26560,26626,26727,27082,27134,31635,31777,31902,31952,32059,32105,32151,32193,32304,32351,32387,103280,104260,104371", "endLines": "251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,287,289,290,293,295,327,372,373,394,395,396,401,402,464,466,468,469,471,472,473,474,476,477,478,1593,1609,1613", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "16695,16754,16814,16874,16934,16994,17054,17114,17174,17234,17294,17354,17413,17473,17533,17593,17653,17713,17773,17833,17893,17953,18013,18072,18132,18192,18251,18310,18369,18428,18487,18546,18825,18943,18998,19172,19261,20916,24024,24078,26621,26722,26780,27129,27189,31692,31826,31947,32001,32100,32146,32188,32228,32346,32382,32472,103387,104366,104561"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/dda665aa4a1576cfb1759fb2bbcd5279/transformed/appcompat-1.7.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "3,4,9,29,34,35,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,59,60,74,75,76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93,94,95,96,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,209,210,211,212,213,214,215,216,217,240,241,242,243,244,245,246,247,283,284,285,286,294,300,301,304,321,328,329,330,331,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,470,482,483,484,485,486,501,509,510,514,518,522,527,533,540,544,548,553,557,561,565,569,573,577,583,587,593,597,603,607,612,616,619,623,629,633,639,643,649,652,656,660,664,668,672,673,674,675,678,681,684,687,691,692,693,694,695,698,700,702,704,709,710,714,720,724,725,727,739,740,744,750,754,758,759,763,790,794,795,799,827,999,1025,1196,1222,1253,1261,1267,1283,1305,1310,1315,1325,1334,1343,1347,1354,1373,1380,1381,1390,1393,1396,1400,1404,1408,1411,1412,1417,1422,1432,1437,1444,1450,1451,1454,1458,1463,1465,1467,1470,1473,1475,1479,1482,1489,1492,1495,1499,1501,1505,1507,1509,1511,1515,1523,1531,1543,1549,1558,1561,1572,1575,1576,1581,1582,1614,1683,1753,1754,1764,1773,1774,1776,1780,1783,1786,1789,1792,1795,1798,1801,1805,1808,1811,1814,1818,1821,1825,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1851,1853,1854,1855,1856,1857,1858,1859,1860,1862,1863,1865,1866,1868,1870,1871,1873,1874,1875,1876,1877,1878,1880,1881,1882,1883,1884,1896,1898,1900,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1916,1917,1918,1919,1920,1921,1922,1924,1928,1958,1959,1960,1961,1962,1963,1967,1968,1969,1970,1972,1974,1976,1978,1980,1981,1982,1983,1985,1987,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2003,2004,2005,2006,2008,2010,2011,2013,2014,2016,2018,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2033,2034,2035,2036,2038,2039,2040,2041,2042,2044,2046,2048,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2070,2145,2148,2151,2154,2168,2184,2226,2229,2258,2285,2294,2358,2721,2731,2769,2887,3009,3033,3039,3058,3079,3203,3223,3229,3237,3243,3297,3357,3423,3443,3498,3510,3536", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "211,266,463,1334,1651,1706,1768,1832,1902,1963,2038,2114,2191,2429,2514,2596,2672,2748,2825,2903,3009,3115,3194,3523,3580,4671,4745,4820,4885,4951,5011,5072,5144,5217,5284,5409,5468,5527,5586,5645,5704,5758,5812,5865,5919,5973,6027,6213,6287,6366,6439,6513,6584,6656,6728,6801,6858,6916,6989,7063,7137,7212,7284,7357,7427,7498,7558,7619,7688,7757,7827,7901,7977,8041,8118,8194,8271,8336,8405,8482,8557,8626,8694,8771,8837,8898,8995,9060,9129,9228,9299,9358,9416,9473,9532,9596,9667,9739,9811,9883,9955,10022,10090,10158,10217,10280,10344,10434,10525,10585,10651,10718,10784,10854,10918,10971,11038,11099,11166,11279,11337,11400,11465,11530,11605,11678,11750,11794,11841,11887,11936,11997,12058,12119,12181,12245,12309,12373,12438,12501,12561,12622,12688,12747,12807,12869,12940,13000,13699,13785,13872,13962,14049,14137,14219,14302,14392,15975,16027,16085,16130,16196,16260,16317,16374,18551,18608,18656,18705,19177,19457,19504,19660,20565,20921,20985,21047,21107,21443,21517,21587,21665,21719,21789,21874,21922,21968,22029,22092,22158,22222,22293,22356,22421,22485,22546,22607,22659,22732,22806,22875,22950,23024,23098,23239,32006,32660,32738,32828,32916,33012,33786,34368,34457,34704,34985,35237,35522,35915,36392,36614,36836,37112,37339,37569,37799,38029,38259,38486,38905,39131,39556,39786,40214,40433,40716,40924,41055,41282,41708,41933,42360,42581,43006,43126,43402,43703,44027,44318,44632,44769,44900,45005,45247,45414,45618,45826,46097,46209,46321,46426,46543,46757,46903,47043,47129,47477,47565,47811,48229,48478,48560,48658,49315,49415,49667,50091,50346,50706,50795,51032,53056,53298,53400,53653,55809,66490,68006,78701,80229,81986,82612,83032,84293,85558,85814,86050,86597,87091,87696,87894,88474,89842,90217,90335,90873,91030,91226,91499,91755,91925,92066,92130,92495,92862,93538,93802,94140,94493,94587,94773,95079,95341,95466,95593,95832,96043,96162,96355,96532,96987,97168,97290,97549,97662,97849,97951,98058,98187,98462,98970,99466,100343,100637,101207,101356,102088,102260,102344,102680,102772,104566,109797,115168,115230,115808,116392,116483,116596,116825,116985,117137,117308,117474,117643,117810,117973,118216,118386,118559,118730,119004,119203,119408,119738,119822,119918,120014,120112,120212,120314,120416,120518,120620,120722,120822,120918,121030,121159,121282,121413,121544,121642,121756,121850,121990,122124,122220,122332,122432,122548,122644,122756,122856,122996,123132,123296,123426,123584,123734,123875,124019,124154,124266,124416,124544,124672,124808,124940,125070,125200,125312,126210,126356,126500,126638,126704,126794,126870,126974,127064,127166,127274,127382,127482,127562,127654,127752,127862,127914,127992,128098,128190,128294,128404,128526,128689,130354,130434,130534,130624,130734,130824,131065,131159,131265,131357,131457,131569,131683,131799,131915,132009,132123,132235,132337,132457,132579,132661,132765,132885,133011,133109,133203,133291,133403,133519,133641,133753,133928,134044,134130,134222,134334,134458,134525,134651,134719,134847,134991,135119,135188,135283,135398,135511,135610,135719,135830,135941,136042,136147,136247,136377,136468,136591,136685,136797,136883,136987,137083,137171,137289,137393,137497,137623,137711,137819,137919,138009,138119,138203,138305,138389,138443,138507,138613,138699,138809,138893,139297,141913,142031,142146,142226,142587,143146,144550,144628,145972,147333,147721,150564,160617,160955,162626,168710,172937,173688,173950,174465,174844,179122,179728,179957,180251,180466,181966,183847,186873,187617,189748,190088,191399", "endLines": "3,4,9,29,34,35,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,59,60,74,75,76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93,94,95,96,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,209,210,211,212,213,214,215,216,217,240,241,242,243,244,245,246,247,283,284,285,286,294,300,301,304,321,328,329,330,331,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,470,482,483,484,485,486,508,509,513,517,521,526,532,539,543,547,552,556,560,564,568,572,576,582,586,592,596,602,606,611,615,618,622,628,632,638,642,648,651,655,659,663,667,671,672,673,674,677,680,683,686,690,691,692,693,694,697,699,701,703,708,709,713,719,723,724,726,738,739,743,749,753,754,758,762,789,793,794,798,826,998,1024,1195,1221,1252,1260,1266,1282,1304,1309,1314,1324,1333,1342,1346,1353,1372,1379,1380,1389,1392,1395,1399,1403,1407,1410,1411,1416,1421,1431,1436,1443,1449,1450,1453,1457,1462,1464,1466,1469,1472,1474,1478,1481,1488,1491,1494,1498,1500,1504,1506,1508,1510,1514,1522,1530,1542,1548,1557,1560,1571,1574,1575,1580,1581,1586,1682,1752,1753,1763,1772,1773,1775,1779,1782,1785,1788,1791,1794,1797,1800,1804,1807,1810,1813,1817,1820,1824,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1850,1852,1853,1854,1855,1856,1857,1858,1859,1861,1862,1864,1865,1867,1869,1870,1872,1873,1874,1875,1876,1877,1879,1880,1881,1882,1883,1884,1897,1899,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1915,1916,1917,1918,1919,1920,1921,1923,1927,1931,1958,1959,1960,1961,1962,1966,1967,1968,1969,1971,1973,1975,1977,1979,1980,1981,1982,1984,1986,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2002,2003,2004,2005,2007,2009,2010,2012,2013,2015,2017,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2032,2033,2034,2035,2037,2038,2039,2040,2041,2043,2045,2047,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2144,2147,2150,2153,2167,2173,2193,2228,2257,2284,2293,2357,2720,2724,2758,2796,2904,3032,3038,3044,3078,3202,3222,3228,3232,3242,3277,3308,3422,3442,3497,3509,3535,3542", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "261,306,507,1370,1701,1763,1827,1897,1958,2033,2109,2186,2264,2509,2591,2667,2743,2820,2898,3004,3110,3189,3269,3575,3633,4740,4815,4880,4946,5006,5067,5139,5212,5279,5347,5463,5522,5581,5640,5699,5753,5807,5860,5914,5968,6022,6076,6282,6361,6434,6508,6579,6651,6723,6796,6853,6911,6984,7058,7132,7207,7279,7352,7422,7493,7553,7614,7683,7752,7822,7896,7972,8036,8113,8189,8266,8331,8400,8477,8552,8621,8689,8766,8832,8893,8990,9055,9124,9223,9294,9353,9411,9468,9527,9591,9662,9734,9806,9878,9950,10017,10085,10153,10212,10275,10339,10429,10520,10580,10646,10713,10779,10849,10913,10966,11033,11094,11161,11274,11332,11395,11460,11525,11600,11673,11745,11789,11836,11882,11931,11992,12053,12114,12176,12240,12304,12368,12433,12496,12556,12617,12683,12742,12802,12864,12935,12995,13063,13780,13867,13957,14044,14132,14214,14297,14387,14478,16022,16080,16125,16191,16255,16312,16369,16423,18603,18651,18700,18751,19206,19499,19548,19701,20592,20980,21042,21102,21159,21512,21582,21660,21714,21784,21869,21917,21963,22024,22087,22153,22217,22288,22351,22416,22480,22541,22602,22654,22727,22801,22870,22945,23019,23093,23234,23304,32054,32733,32823,32911,33007,33097,34363,34452,34699,34980,35232,35517,35910,36387,36609,36831,37107,37334,37564,37794,38024,38254,38481,38900,39126,39551,39781,40209,40428,40711,40919,41050,41277,41703,41928,42355,42576,43001,43121,43397,43698,44022,44313,44627,44764,44895,45000,45242,45409,45613,45821,46092,46204,46316,46421,46538,46752,46898,47038,47124,47472,47560,47806,48224,48473,48555,48653,49310,49410,49662,50086,50341,50435,50790,51027,53051,53293,53395,53648,55804,66485,68001,78696,80224,81981,82607,83027,84288,85553,85809,86045,86592,87086,87691,87889,88469,89837,90212,90330,90868,91025,91221,91494,91750,91920,92061,92125,92490,92857,93533,93797,94135,94488,94582,94768,95074,95336,95461,95588,95827,96038,96157,96350,96527,96982,97163,97285,97544,97657,97844,97946,98053,98182,98457,98965,99461,100338,100632,101202,101351,102083,102255,102339,102675,102767,103045,109792,115163,115225,115803,116387,116478,116591,116820,116980,117132,117303,117469,117638,117805,117968,118211,118381,118554,118725,118999,119198,119403,119733,119817,119913,120009,120107,120207,120309,120411,120513,120615,120717,120817,120913,121025,121154,121277,121408,121539,121637,121751,121845,121985,122119,122215,122327,122427,122543,122639,122751,122851,122991,123127,123291,123421,123579,123729,123870,124014,124149,124261,124411,124539,124667,124803,124935,125065,125195,125307,125447,126351,126495,126633,126699,126789,126865,126969,127059,127161,127269,127377,127477,127557,127649,127747,127857,127909,127987,128093,128185,128289,128399,128521,128684,128841,130429,130529,130619,130729,130819,131060,131154,131260,131352,131452,131564,131678,131794,131910,132004,132118,132230,132332,132452,132574,132656,132760,132880,133006,133104,133198,133286,133398,133514,133636,133748,133923,134039,134125,134217,134329,134453,134520,134646,134714,134842,134986,135114,135183,135278,135393,135506,135605,135714,135825,135936,136037,136142,136242,136372,136463,136586,136680,136792,136878,136982,137078,137166,137284,137388,137492,137618,137706,137814,137914,138004,138114,138198,138300,138384,138438,138502,138608,138694,138804,138888,139008,141908,142026,142141,142221,142582,142815,143658,144623,145967,147328,147716,150559,160612,160747,162320,163978,169277,173683,173945,174145,174839,179117,179723,179952,180103,180461,181544,182273,186868,187612,189743,190083,191394,191597"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,157,178,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8915,9596,10278", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,156,177,210,216", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8910,9591,10273,10440"}, "to": {"startLines": "10,43,44,61,62,97,98,202,203,204,205,206,207,208,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,248,249,250,297,298,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,335,365,366,367,368,369,370,371,475,1885,1886,1890,1891,1895,2065,2066,2725,2759,2905,2940,2970,3003", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "512,2269,2341,3638,3703,6081,6150,13211,13281,13349,13421,13491,13552,13626,14483,14544,14605,14667,14731,14793,14854,14922,15022,15082,15148,15221,15290,15347,15399,16428,16500,16576,19320,19355,19706,19761,19824,19879,19937,19995,20056,20119,20176,20227,20277,20338,20395,20461,20495,20530,21373,23453,23520,23592,23661,23730,23804,23876,32233,125452,125569,125770,125880,126081,139013,139085,160752,162325,169282,171088,172088,172770", "endLines": "10,43,44,61,62,97,98,202,203,204,205,206,207,208,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,248,249,250,297,298,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,335,365,366,367,368,369,370,371,475,1885,1889,1890,1894,1895,2065,2066,2730,2768,2939,2960,3002,3008", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "567,2336,2424,3698,3764,6145,6208,13276,13344,13416,13486,13547,13621,13694,14539,14600,14662,14726,14788,14849,14917,15017,15077,15143,15216,15285,15342,15394,15456,16495,16571,16636,19350,19385,19756,19819,19874,19932,19990,20051,20114,20171,20222,20272,20333,20390,20456,20490,20525,20560,21438,23515,23587,23656,23725,23799,23871,23959,32299,125564,125765,125875,126076,126205,139080,139147,160950,162621,171083,171764,172765,172932"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/84189f789b5e554413b94a7106c44443/transformed/material3-release/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "334,403,404,405,406,407,408,409,410,411,412,415,416,417,418,419,420,421,422,423,424,425,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,1594,1604", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21300,27194,27282,27368,27449,27533,27602,27667,27750,27856,27942,28062,28116,28185,28246,28315,28404,28499,28573,28670,28763,28861,29010,29101,29189,29285,29383,29447,29515,29602,29696,29763,29835,29907,30008,30117,30193,30262,30310,30376,30440,30514,30571,30628,30700,30750,30804,30875,30946,31016,31085,31143,31219,31290,31364,31450,31500,31570,103392,104107", "endLines": "334,403,404,405,406,407,408,409,410,411,414,415,416,417,418,419,420,421,422,423,424,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,1603,1606", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "21368,27277,27363,27444,27528,27597,27662,27745,27851,27937,28057,28111,28180,28241,28310,28399,28494,28568,28665,28758,28856,29005,29096,29184,29280,29378,29442,29510,29597,29691,29758,29830,29902,30003,30112,30188,30257,30305,30371,30435,30509,30566,30623,30695,30745,30799,30870,30941,31011,31080,31138,31214,31285,31359,31445,31495,31565,31630,104102,104255"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/50f3617f2a68b81da53c423be34739c9/transformed/lifecycle-viewmodel-release/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "325", "startColumns": "4", "startOffsets": "20754", "endColumns": "49", "endOffsets": "20799"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/res/values/values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "333,382", "startColumns": "4,4", "startOffsets": "21232,25123", "endColumns": "67,166", "endOffsets": "21295,25285"}}, {"source": "/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/res/values/strings.xml", "from": {"startLines": "2,5,4,3", "startColumns": "4,4,4,4", "startOffsets": "55,259,188,116", "endColumns": "60,75,70,71", "endOffsets": "111,330,254,183"}, "to": {"startLines": "364,393,467,479", "startColumns": "4,4,4,4", "startOffsets": "23392,26484,31831,32477", "endColumns": "60,75,70,71", "endOffsets": "23448,26555,31897,32544"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/fbae0b886760a9e76e330106e8598bdf/transformed/customview-poolingcontainer-1.0.0/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "296,299", "startColumns": "4,4", "startOffsets": "19266,19390", "endColumns": "53,66", "endOffsets": "19315,19452"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/res/values/values.xml", "from": {"startLines": "2,7,8,9,10,11,19,23,34,51", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,234,294,346,391,451,869,1066,1790,2904", "endLines": "6,7,8,9,10,18,22,33,50,58", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "229,289,341,386,446,864,1061,1785,2899,3292"}, "to": {"startLines": "21,26,27,28,288,2174,2180,3309,3317,3329", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "998,1177,1237,1289,18830,142820,143015,182278,182560,183000", "endLines": "25,26,27,28,288,2179,2183,3316,3328,3336", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "1172,1232,1284,1329,18885,143010,143141,182555,182995,183304"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/998c918bf96ae2f6a4f5c8c644413a6f/transformed/savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "324", "startColumns": "4", "startOffsets": "20700", "endColumns": "53", "endOffsets": "20749"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "400", "startColumns": "4", "startOffsets": "27000", "endColumns": "81", "endOffsets": "27077"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/7a4193c6fbbe5e128015b7f6283124c0/transformed/fragment-1.8.4/res/values/values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "291,303,326,2961,2966", "startColumns": "4,4,4,4,4", "startOffsets": "19003,19595,20804,171769,171939", "endLines": "291,303,326,2965,2969", "endColumns": "56,64,63,24,24", "endOffsets": "19055,19655,20863,171934,172083"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/ce7a7439fda0c36e77102a7d38ff3226/transformed/browser-1.8.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "55,56,57,58,200,201,392,397,398,399", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3274,3332,3398,3461,13068,13139,26416,26785,26852,26931", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3327,3393,3456,3518,13134,13206,26479,26847,26926,26995"}}, {"source": "/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/res/values/ic_launcher_background.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "56", "endOffsets": "107"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "5352", "endColumns": "56", "endOffsets": "5404"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/16884767c054ac4cab0f70a5a4855d4d/transformed/appcompat-resources-1.7.0/res/values/values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2194,2210,2216,3337,3353", "startColumns": "4,4,4,4,4", "startOffsets": "143663,144088,144266,183309,183720", "endLines": "2209,2215,2225,3352,3356", "endColumns": "24,24,24,24,24", "endOffsets": "144083,144261,144545,183715,183842"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/d8c531c2afb462bf72eed5fcccc5521b/transformed/activity-1.9.3/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "302,323", "startColumns": "4,4", "startOffsets": "19553,20640", "endColumns": "41,59", "endOffsets": "19590,20695"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/80c866390182a769f8356a80ded4035a/transformed/foundation-release/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "480,481", "startColumns": "4,4", "startOffsets": "32549,32605", "endColumns": "55,54", "endOffsets": "32600,32655"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/1752430dcf0de4b1c1fddcd7c40ca3c4/transformed/ui-graphics-release/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "292", "startColumns": "4", "startOffsets": "19060", "endColumns": "65", "endOffsets": "19121"}}, {"source": "/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/res/values/styles.xml", "from": {"startLines": "4,11,18", "startColumns": "4,4,4", "startOffsets": "93,413,664", "endLines": "9,15,20", "endColumns": "12,12,12", "endOffsets": "407,657,810"}, "to": {"startLines": "487,493,498", "startColumns": "4,4,4", "startOffsets": "33102,33386,33635", "endLines": "492,497,500", "endColumns": "12,12,12", "endOffsets": "33381,33630,33781"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/73dcf6b3a582150357036810e8cb6e2f/transformed/lifecycle-runtime-release/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "322", "startColumns": "4", "startOffsets": "20597", "endColumns": "42", "endOffsets": "20635"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/res/values/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "66,67,68,69,70,71,72,73,374,375,376,377,378,379,380,381,383,384,385,386,387,388,389,390,391,3045,3278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4000,4090,4170,4260,4350,4430,4511,4591,24083,24188,24369,24494,24601,24781,24904,25020,25290,25478,25583,25764,25889,26064,26212,26275,26337,174150,181549", "endLines": "66,67,68,69,70,71,72,73,374,375,376,377,378,379,380,381,383,384,385,386,387,388,389,390,391,3057,3296", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4085,4165,4255,4345,4425,4506,4586,4666,24183,24364,24489,24596,24776,24899,25015,25118,25473,25578,25759,25884,26059,26207,26270,26332,26411,174460,181961"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/211afc976588db4fa106afcb827907e0/transformed/camera-view-1.4.0/res/values/values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "5,12,3233", "startColumns": "4,4,4", "startOffsets": "311,632,180108", "endLines": "8,19,3236", "endColumns": "11,11,24", "endOffsets": "458,934,180246"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/0c35e3b0c2fe34519a603108fedf6f64/transformed/startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "363", "startColumns": "4", "startOffsets": "23309", "endColumns": "82", "endOffsets": "23387"}}]}]}