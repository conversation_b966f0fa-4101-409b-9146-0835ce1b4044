{"logs": [{"outputFile": "my.supershoppe.app-mergeDebugResources-63:/values-vi/values-vi.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/84189f789b5e554413b94a7106c44443/transformed/material3-release/res/values-vi/values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,289,396,513,614,709,821,958,1078,1219,1303,1406,1495,1591,1710,1833,1941,2068,2191,2318,2477,2604,2727,2847,2966,3056,3156,3274,3407,3502,3608,3715,3838,3968,4076,4172,4251,4348,4444,4555,4644,4728,4835,4915,4998,5097,5195,5290,5389,5475,5576,5674,5776,5892,5972,6081", "endColumns": "118,114,106,116,100,94,111,136,119,140,83,102,88,95,118,122,107,126,122,126,158,126,122,119,118,89,99,117,132,94,105,106,122,129,107,95,78,96,95,110,88,83,106,79,82,98,97,94,98,85,100,97,101,115,79,108,103", "endOffsets": "169,284,391,508,609,704,816,953,1073,1214,1298,1401,1490,1586,1705,1828,1936,2063,2186,2313,2472,2599,2722,2842,2961,3051,3151,3269,3402,3497,3603,3710,3833,3963,4071,4167,4246,4343,4439,4550,4639,4723,4830,4910,4993,5092,5190,5285,5384,5470,5571,5669,5771,5887,5967,6076,6180"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6873,6992,7107,7214,7331,7432,7527,7639,7776,7896,8037,8121,8224,8313,8409,8528,8651,8759,8886,9009,9136,9295,9422,9545,9665,9784,9874,9974,10092,10225,10320,10426,10533,10656,10786,10894,10990,11069,11166,11262,11373,11462,11546,11653,11733,11816,11915,12013,12108,12207,12293,12394,12492,12594,12710,12790,12899", "endColumns": "118,114,106,116,100,94,111,136,119,140,83,102,88,95,118,122,107,126,122,126,158,126,122,119,118,89,99,117,132,94,105,106,122,129,107,95,78,96,95,110,88,83,106,79,82,98,97,94,98,85,100,97,101,115,79,108,103", "endOffsets": "6987,7102,7209,7326,7427,7522,7634,7771,7891,8032,8116,8219,8308,8404,8523,8646,8754,8881,9004,9131,9290,9417,9540,9660,9779,9869,9969,10087,10220,10315,10421,10528,10651,10781,10889,10985,11064,11161,11257,11368,11457,11541,11648,11728,11811,11910,12008,12103,12202,12288,12389,12487,12589,12705,12785,12894,12998"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/res/values-vi/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,449,570,675,836,962,1077,1177,1346,1449,1602,1728,1883,2028,2092,2152", "endColumns": "97,157,120,104,160,125,114,99,168,102,152,125,154,144,63,59,78", "endOffsets": "290,448,569,674,835,961,1076,1176,1345,1448,1601,1727,1882,2027,2091,2151,2230"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3710,3812,3974,4099,4208,4373,4503,4622,4854,5027,5134,5291,5421,5580,5729,5797,5861", "endColumns": "101,161,124,108,164,129,118,103,172,106,156,129,158,148,67,63,82", "endOffsets": "3807,3969,4094,4203,4368,4498,4617,4721,5022,5129,5286,5416,5575,5724,5792,5856,5939"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/res/values-vi/values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "29,30,31,32,33,34,35,131", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2798,2895,2997,3096,3196,3299,3412,13745", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "2890,2992,3091,3191,3294,3407,3523,13841"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/res/values-vi/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "123", "endOffsets": "318"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4726", "endColumns": "127", "endOffsets": "4849"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/a8dee9b18ef2037d25f00c7473086895/transformed/ui-release/res/values-vi/values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,393,493,585,670,763,857,938,1028,1119,1191,1267,1344,1420,1497,1563", "endColumns": "95,85,105,99,91,84,92,93,80,89,90,71,75,76,75,76,65,113", "endOffsets": "196,282,388,488,580,665,758,852,933,1023,1114,1186,1262,1339,1415,1492,1558,1672"}, "to": {"startLines": "36,37,57,58,59,63,64,122,123,124,125,127,128,129,130,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3528,3624,6061,6167,6267,6695,6780,13003,13097,13178,13268,13444,13516,13592,13669,13846,13923,13989", "endColumns": "95,85,105,99,91,84,92,93,80,89,90,71,75,76,75,76,65,113", "endOffsets": "3619,3705,6162,6262,6354,6775,6868,13092,13173,13263,13354,13511,13587,13664,13740,13918,13984,14098"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/ce7a7439fda0c36e77102a7d38ff3226/transformed/browser-1.8.0/res/values-vi/values-vi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,283,397", "endColumns": "116,110,113,110", "endOffsets": "167,278,392,503"}, "to": {"startLines": "56,60,61,62", "startColumns": "4,4,4,4", "startOffsets": "5944,6359,6470,6584", "endColumns": "116,110,113,110", "endOffsets": "6056,6465,6579,6690"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/dda665aa4a1576cfb1759fb2bbcd5279/transformed/appcompat-1.7.0/res/values-vi/values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,13359", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,13439"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/80c866390182a769f8356a80ded4035a/transformed/foundation-release/res/values-vi/values-vi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,86", "endOffsets": "138,225"}, "to": {"startLines": "135,136", "startColumns": "4,4", "startOffsets": "14103,14191", "endColumns": "87,86", "endOffsets": "14186,14273"}}]}]}