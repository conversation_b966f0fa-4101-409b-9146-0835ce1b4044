{"logs": [{"outputFile": "my.supershoppe.app-mergeDebugResources-63:/values-pa/values-pa.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/ce7a7439fda0c36e77102a7d38ff3226/transformed/browser-1.8.0/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,261,375", "endColumns": "104,100,113,102", "endOffsets": "155,256,370,473"}, "to": {"startLines": "56,60,61,62", "startColumns": "4,4,4,4", "startOffsets": "5938,6322,6423,6537", "endColumns": "104,100,113,102", "endOffsets": "6038,6418,6532,6635"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/dda665aa4a1576cfb1759fb2bbcd5279/transformed/appcompat-1.7.0/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,13332", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,13407"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/res/values-pa/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,591,696,839,964,1073,1172,1330,1435,1604,1732,1881,2038,2099,2161", "endColumns": "102,168,125,104,142,124,108,98,157,104,168,127,148,156,60,61,77", "endOffsets": "295,464,590,695,838,963,1072,1171,1329,1434,1603,1731,1880,2037,2098,2160,2238"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3673,3780,3953,4083,4192,4339,4468,4581,4835,4997,5106,5279,5411,5564,5725,5790,5856", "endColumns": "106,172,129,108,146,128,112,102,161,108,172,131,152,160,64,65,81", "endOffsets": "3775,3948,4078,4187,4334,4463,4576,4679,4992,5101,5274,5406,5559,5720,5785,5851,5933"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/res/values-pa/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4684", "endColumns": "150", "endOffsets": "4830"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,558,656,785", "endColumns": "97,101,99,100,101,97,128,100", "endOffsets": "148,250,350,451,553,651,780,881"}, "to": {"startLines": "29,30,31,32,33,34,35,131", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2767,2865,2967,3067,3168,3270,3368,13709", "endColumns": "97,101,99,100,101,97,128,100", "endOffsets": "2860,2962,3062,3163,3265,3363,3492,13805"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/a8dee9b18ef2037d25f00c7473086895/transformed/ui-release/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,373,472,560,638,736,824,908,987,1068,1140,1215,1290,1365,1445,1511", "endColumns": "92,82,91,98,87,77,97,87,83,78,80,71,74,74,74,79,65,117", "endOffsets": "193,276,368,467,555,633,731,819,903,982,1063,1135,1210,1285,1360,1440,1506,1624"}, "to": {"startLines": "36,37,57,58,59,63,64,122,123,124,125,127,128,129,130,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3497,3590,6043,6135,6234,6640,6718,13000,13088,13172,13251,13412,13484,13559,13634,13810,13890,13956", "endColumns": "92,82,91,98,87,77,97,87,83,78,80,71,74,74,74,79,65,117", "endOffsets": "3585,3668,6130,6229,6317,6713,6811,13083,13167,13246,13327,13479,13554,13629,13704,13885,13951,14069"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/80c866390182a769f8356a80ded4035a/transformed/foundation-release/res/values-pa/values-pa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,86", "endOffsets": "137,224"}, "to": {"startLines": "135,136", "startColumns": "4,4", "startOffsets": "14074,14161", "endColumns": "86,86", "endOffsets": "14156,14243"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/84189f789b5e554413b94a7106c44443/transformed/material3-release/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,291,407,526,623,724,842,980,1104,1247,1332,1435,1525,1622,1734,1855,1963,2098,2235,2366,2532,2658,2773,2892,3012,3103,3199,3318,3454,3556,3659,3765,3897,4035,4146,4245,4321,4418,4519,4631,4716,4804,4903,4983,5067,5167,5266,5361,5459,5545,5646,5744,5846,5961,6041,6143", "endColumns": "116,118,115,118,96,100,117,137,123,142,84,102,89,96,111,120,107,134,136,130,165,125,114,118,119,90,95,118,135,101,102,105,131,137,110,98,75,96,100,111,84,87,98,79,83,99,98,94,97,85,100,97,101,114,79,101,95", "endOffsets": "167,286,402,521,618,719,837,975,1099,1242,1327,1430,1520,1617,1729,1850,1958,2093,2230,2361,2527,2653,2768,2887,3007,3098,3194,3313,3449,3551,3654,3760,3892,4030,4141,4240,4316,4413,4514,4626,4711,4799,4898,4978,5062,5162,5261,5356,5454,5540,5641,5739,5841,5956,6036,6138,6234"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6816,6933,7052,7168,7287,7384,7485,7603,7741,7865,8008,8093,8196,8286,8383,8495,8616,8724,8859,8996,9127,9293,9419,9534,9653,9773,9864,9960,10079,10215,10317,10420,10526,10658,10796,10907,11006,11082,11179,11280,11392,11477,11565,11664,11744,11828,11928,12027,12122,12220,12306,12407,12505,12607,12722,12802,12904", "endColumns": "116,118,115,118,96,100,117,137,123,142,84,102,89,96,111,120,107,134,136,130,165,125,114,118,119,90,95,118,135,101,102,105,131,137,110,98,75,96,100,111,84,87,98,79,83,99,98,94,97,85,100,97,101,114,79,101,95", "endOffsets": "6928,7047,7163,7282,7379,7480,7598,7736,7860,8003,8088,8191,8281,8378,8490,8611,8719,8854,8991,9122,9288,9414,9529,9648,9768,9859,9955,10074,10210,10312,10415,10521,10653,10791,10902,11001,11077,11174,11275,11387,11472,11560,11659,11739,11823,11923,12022,12117,12215,12301,12402,12500,12602,12717,12797,12899,12995"}}]}]}