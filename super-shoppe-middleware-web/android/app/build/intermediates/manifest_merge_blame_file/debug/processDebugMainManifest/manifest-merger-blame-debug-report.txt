1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="my.supershoppe.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12
13    <uses-permission android:name="android.permission.INTERNET" />
13-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:40:5-67
13-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:40:22-64
14
15    <queries>
15-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-11:15
16        <intent>
16-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-10:18
17            <action android:name="android.support.customtabs.action.CustomTabsService" />
17-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-90
17-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:21-87
18        </intent>
19    </queries>
20
21    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
21-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:22:5-79
21-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:22:22-76
22    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
22-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:23:5-77
22-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:23:22-74
23    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
23-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:24:5-68
23-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:24:22-65
24    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
24-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:26:5-82
24-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:26:22-79
25
26    <permission
26-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:22:5-24:47
27        android:name="my.supershoppe.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="my.supershoppe.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
30-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:26:22-94
31    <uses-permission android:name="android.permission.CAMERA" />
31-->[io.ionic.libs:ionbarcode-android:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7bf83b626d16f9a93e391c001ed93a7/transformed/ionbarcode-android-1.2.0/AndroidManifest.xml:7:5-65
31-->[io.ionic.libs:ionbarcode-android:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7bf83b626d16f9a93e391c001ed93a7/transformed/ionbarcode-android-1.2.0/AndroidManifest.xml:7:22-62
32
33    <application
33-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:4:5-36:19
34        android:allowBackup="true"
34-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:5:9-35
35        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
35-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:28:18-86
36        android:debuggable="true"
37        android:extractNativeLibs="false"
38        android:icon="@mipmap/ic_launcher"
38-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:6:9-43
39        android:label="@string/app_name"
39-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:7:9-41
40        android:roundIcon="@mipmap/ic_launcher_round"
40-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:8:9-54
41        android:supportsRtl="true"
41-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:9:9-35
42        android:testOnly="true"
43        android:theme="@style/AppTheme" >
43-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:10:9-40
44        <activity
44-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:12:9-25:20
45            android:name="my.supershoppe.app.MainActivity"
45-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:14:13-41
46            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
46-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:13:13-140
47            android:exported="true"
47-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:18:13-36
48            android:label="@string/title_activity_main"
48-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:15:13-56
49            android:launchMode="singleTask"
49-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:17:13-44
50            android:theme="@style/AppTheme.NoActionBarLaunch" >
50-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:16:13-62
51            <intent-filter>
51-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:20:13-23:29
52                <action android:name="android.intent.action.MAIN" />
52-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:21:17-69
52-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:21:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:22:17-77
54-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:22:27-74
55            </intent-filter>
56        </activity>
57
58        <provider
59            android:name="androidx.core.content.FileProvider"
59-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:28:13-62
60            android:authorities="my.supershoppe.app.fileprovider"
60-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:29:13-64
61            android:exported="false"
61-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:30:13-37
62            android:grantUriPermissions="true" >
62-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:31:13-47
63            <meta-data
63-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:32:13-34:64
64                android:name="android.support.FILE_PROVIDER_PATHS"
64-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:33:17-67
65                android:resource="@xml/file_paths" />
65-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:34:17-51
66        </provider>
67
68        <activity
68-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:9-18:75
69            android:name="com.capacitorjs.plugins.browser.BrowserControllerActivity"
69-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-85
70            android:exported="false"
70-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
71            android:launchMode="singleTask"
71-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-44
72            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
72-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:13-72
73
74        <service
74-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-14:19
75            android:name="com.capacitorjs.plugins.pushnotifications.MessagingService"
75-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-86
76            android:exported="false" >
76-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
77            <intent-filter>
77-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-13:29
78                <action android:name="com.google.firebase.MESSAGING_EVENT" />
78-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:17-78
78-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:25-75
79            </intent-filter>
80        </service>
81        <!--
82        Service for holding metadata. Cannot be instantiated.
83        Metadata will be merged from other manifests.
84        -->
85        <service
85-->[androidx.camera:camera-core:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7f66536c5c1ee456d59e4d79dbad951/transformed/camera-core-1.4.0/AndroidManifest.xml:29:9-33:78
86            android:name="androidx.camera.core.impl.MetadataHolderService"
86-->[androidx.camera:camera-core:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7f66536c5c1ee456d59e4d79dbad951/transformed/camera-core-1.4.0/AndroidManifest.xml:30:13-75
87            android:enabled="false"
87-->[androidx.camera:camera-core:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7f66536c5c1ee456d59e4d79dbad951/transformed/camera-core-1.4.0/AndroidManifest.xml:31:13-36
88            android:exported="false" >
88-->[androidx.camera:camera-core:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7f66536c5c1ee456d59e4d79dbad951/transformed/camera-core-1.4.0/AndroidManifest.xml:32:13-37
89            <meta-data
89-->[androidx.camera:camera-camera2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ddf9110bf974cf4ff4f36020e3a3d166/transformed/camera-camera2-1.4.0/AndroidManifest.xml:30:13-32:89
90                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
90-->[androidx.camera:camera-camera2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ddf9110bf974cf4ff4f36020e3a3d166/transformed/camera-camera2-1.4.0/AndroidManifest.xml:31:17-103
91                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
91-->[androidx.camera:camera-camera2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ddf9110bf974cf4ff4f36020e3a3d166/transformed/camera-camera2-1.4.0/AndroidManifest.xml:32:17-86
92        </service>
93        <service
93-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f5c910be0583abc17910421310df0d/transformed/play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:9:9-15:19
94            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
94-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f5c910be0583abc17910421310df0d/transformed/play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:10:13-91
95            android:directBootAware="true"
95-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:17:13-43
96            android:exported="false" >
96-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f5c910be0583abc17910421310df0d/transformed/play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:11:13-37
97            <meta-data
97-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f5c910be0583abc17910421310df0d/transformed/play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:12:13-14:85
98                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
98-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f5c910be0583abc17910421310df0d/transformed/play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:13:17-120
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f5c910be0583abc17910421310df0d/transformed/play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:14:17-82
100            <meta-data
100-->[com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e0ce33f42f27f702f4ff85a02894c3e/transformed/vision-common-17.3.0/AndroidManifest.xml:12:13-14:85
101                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
101-->[com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e0ce33f42f27f702f4ff85a02894c3e/transformed/vision-common-17.3.0/AndroidManifest.xml:13:17-124
102                android:value="com.google.firebase.components.ComponentRegistrar" />
102-->[com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e0ce33f42f27f702f4ff85a02894c3e/transformed/vision-common-17.3.0/AndroidManifest.xml:14:17-82
103            <meta-data
103-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:20:13-22:85
104                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
104-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:21:17-120
105                android:value="com.google.firebase.components.ComponentRegistrar" />
105-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:22:17-82
106        </service>
107
108        <provider
108-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:9:9-13:38
109            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
109-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:10:13-78
110            android:authorities="my.supershoppe.app.mlkitinitprovider"
110-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:11:13-69
111            android:exported="false"
111-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:12:13-37
112            android:initOrder="99" />
112-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:13:13-35
113
114        <receiver
114-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:29:9-40:20
115            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
115-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:30:13-78
116            android:exported="true"
116-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:31:13-36
117            android:permission="com.google.android.c2dm.permission.SEND" >
117-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:32:13-73
118            <intent-filter>
118-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:33:13-35:29
119                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
119-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:34:17-81
119-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:34:25-78
120            </intent-filter>
121
122            <meta-data
122-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:37:13-39:40
123                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
123-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:38:17-92
124                android:value="true" />
124-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:39:17-37
125        </receiver>
126        <!--
127             FirebaseMessagingService performs security checks at runtime,
128             but set to not exported to explicitly avoid allowing another app to call it.
129        -->
130        <service
130-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:46:9-53:19
131            android:name="com.google.firebase.messaging.FirebaseMessagingService"
131-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:47:13-82
132            android:directBootAware="true"
132-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:48:13-43
133            android:exported="false" >
133-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:49:13-37
134            <intent-filter android:priority="-500" >
134-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-13:29
135                <action android:name="com.google.firebase.MESSAGING_EVENT" />
135-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:17-78
135-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:25-75
136            </intent-filter>
137        </service>
138        <service
138-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:54:9-63:19
139            android:name="com.google.firebase.components.ComponentDiscoveryService"
139-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:55:13-84
140            android:directBootAware="true"
140-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:32:13-43
141            android:exported="false" >
141-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:56:13-37
142            <meta-data
142-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:57:13-59:85
143                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
143-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:58:17-122
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:59:17-82
145            <meta-data
145-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:60:13-62:85
146                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
146-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:61:17-119
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:62:17-82
148            <meta-data
148-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:15:13-17:85
149                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
149-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:16:17-130
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:17:17-82
151            <meta-data
151-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:18:13-20:85
152                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
152-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:19:17-127
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:20:17-82
154            <meta-data
154-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/324fc306ed84dc357040da54cc5f1fbc/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
155                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
155-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/324fc306ed84dc357040da54cc5f1fbc/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/324fc306ed84dc357040da54cc5f1fbc/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
157            <meta-data
157-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
158                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
158-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:36:17-109
159                android:value="com.google.firebase.components.ComponentRegistrar" />
159-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:37:17-82
160            <meta-data
160-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
161                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
161-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
162                android:value="com.google.firebase.components.ComponentRegistrar" />
162-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
163        </service>
164
165        <activity
165-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:9-173
166            android:name="com.google.android.gms.common.api.GoogleApiActivity"
166-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:19-85
167            android:exported="false"
167-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:146-170
168            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
168-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:86-145
169
170        <provider
170-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
171            android:name="com.google.firebase.provider.FirebaseInitProvider"
171-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:24:13-77
172            android:authorities="my.supershoppe.app.firebaseinitprovider"
172-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:25:13-72
173            android:directBootAware="true"
173-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:26:13-43
174            android:exported="false"
174-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:27:13-37
175            android:initOrder="100" />
175-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:28:13-36
176
177        <uses-library
177-->[androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/AndroidManifest.xml:25:9-27:40
178            android:name="androidx.window.extensions"
178-->[androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/AndroidManifest.xml:26:13-54
179            android:required="false" />
179-->[androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/AndroidManifest.xml:27:13-37
180        <uses-library
180-->[androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/AndroidManifest.xml:28:9-30:40
181            android:name="androidx.window.sidecar"
181-->[androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/AndroidManifest.xml:29:13-51
182            android:required="false" />
182-->[androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/AndroidManifest.xml:30:13-37
183
184        <provider
184-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
185            android:name="androidx.startup.InitializationProvider"
185-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
186            android:authorities="my.supershoppe.app.androidx-startup"
186-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
187            android:exported="false" >
187-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
188            <meta-data
188-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
189                android:name="androidx.emoji2.text.EmojiCompatInitializer"
189-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
190                android:value="androidx.startup" />
190-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
191            <meta-data
191-->[androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/2aa292f5dbed1ed8a4c314673eb3a237/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:29:13-31:52
192                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
192-->[androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/2aa292f5dbed1ed8a4c314673eb3a237/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:30:17-78
193                android:value="androidx.startup" />
193-->[androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/2aa292f5dbed1ed8a4c314673eb3a237/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:31:17-49
194            <meta-data
194-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
195                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
195-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
196                android:value="androidx.startup" />
196-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
197        </provider>
198
199        <meta-data
199-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
200            android:name="com.google.android.gms.version"
200-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:20-65
201            android:value="@integer/google_play_services_version" />
201-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:66-119
202
203        <receiver
203-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
204            android:name="androidx.profileinstaller.ProfileInstallReceiver"
204-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
205            android:directBootAware="false"
205-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
206            android:enabled="true"
206-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
207            android:exported="true"
207-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
208            android:permission="android.permission.DUMP" >
208-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
209            <intent-filter>
209-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
210                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
210-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
210-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
211            </intent-filter>
212            <intent-filter>
212-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
213                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
213-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
213-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
214            </intent-filter>
215            <intent-filter>
215-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
216                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
216-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
216-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
217            </intent-filter>
218            <intent-filter>
218-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
219                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
219-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
219-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
220            </intent-filter>
221        </receiver>
222
223        <service
223-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
224            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
224-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
225            android:exported="false" >
225-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
226            <meta-data
226-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
227                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
227-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
228                android:value="cct" />
228-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
229        </service>
230        <service
230-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
231            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
231-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
232            android:exported="false"
232-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
233            android:permission="android.permission.BIND_JOB_SERVICE" >
233-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
234        </service>
235
236        <receiver
236-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
237            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
237-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
238            android:exported="false" />
238-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
239
240        <activity
240-->[io.ionic.libs:ionbarcode-android:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7bf83b626d16f9a93e391c001ed93a7/transformed/ionbarcode-android-1.2.0/AndroidManifest.xml:10:9-12:40
241            android:name="com.outsystems.plugins.barcode.view.OSBARCScannerActivity"
241-->[io.ionic.libs:ionbarcode-android:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7bf83b626d16f9a93e391c001ed93a7/transformed/ionbarcode-android-1.2.0/AndroidManifest.xml:11:13-85
242            android:exported="false" />
242-->[io.ionic.libs:ionbarcode-android:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7bf83b626d16f9a93e391c001ed93a7/transformed/ionbarcode-android-1.2.0/AndroidManifest.xml:12:13-37
243    </application>
244
245</manifest>
