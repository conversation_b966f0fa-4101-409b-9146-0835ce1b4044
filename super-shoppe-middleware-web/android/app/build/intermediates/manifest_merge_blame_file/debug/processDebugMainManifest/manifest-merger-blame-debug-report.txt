1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="my.supershoppe.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12
13    <uses-permission android:name="android.permission.INTERNET" />
13-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:40:5-67
13-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:40:22-64
14
15    <queries>
15-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-11:15
16        <intent>
16-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-10:18
17            <action android:name="android.support.customtabs.action.CustomTabsService" />
17-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-90
17-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:21-87
18        </intent>
19    </queries>
20
21    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
21-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:22:5-79
21-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:22:22-76
22    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
22-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:23:5-77
22-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:23:22-74
23    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
23-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:24:5-68
23-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:24:22-65
24    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
24-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:26:5-82
24-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:26:22-79
25
26    <permission
26-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:22:5-24:47
27        android:name="my.supershoppe.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="my.supershoppe.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
30-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:26:22-94
31
32    <application
32-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:4:5-36:19
33        android:allowBackup="true"
33-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:5:9-35
34        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
34-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:28:18-86
35        android:debuggable="true"
36        android:extractNativeLibs="false"
37        android:icon="@mipmap/ic_launcher"
37-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:6:9-43
38        android:label="@string/app_name"
38-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:7:9-41
39        android:roundIcon="@mipmap/ic_launcher_round"
39-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:8:9-54
40        android:supportsRtl="true"
40-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:9:9-35
41        android:testOnly="true"
42        android:theme="@style/AppTheme"
42-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:10:9-40
43        android:usesCleartextTraffic="true" >
43-->[:capacitor-cordova-android-plugins] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:18-53
44        <activity
44-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:12:9-25:20
45            android:name="my.supershoppe.app.MainActivity"
45-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:14:13-41
46            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
46-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:13:13-140
47            android:exported="true"
47-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:18:13-36
48            android:label="@string/title_activity_main"
48-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:15:13-56
49            android:launchMode="singleTask"
49-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:17:13-44
50            android:theme="@style/AppTheme.NoActionBarLaunch" >
50-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:16:13-62
51            <intent-filter>
51-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:20:13-23:29
52                <action android:name="android.intent.action.MAIN" />
52-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:21:17-69
52-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:21:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:22:17-77
54-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:22:27-74
55            </intent-filter>
56        </activity>
57
58        <provider
59            android:name="androidx.core.content.FileProvider"
59-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:28:13-62
60            android:authorities="my.supershoppe.app.fileprovider"
60-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:29:13-64
61            android:exported="false"
61-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:30:13-37
62            android:grantUriPermissions="true" >
62-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:31:13-47
63            <meta-data
63-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:32:13-34:64
64                android:name="android.support.FILE_PROVIDER_PATHS"
64-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:33:17-67
65                android:resource="@xml/file_paths" />
65-->/Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:34:17-51
66        </provider>
67
68        <activity
68-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:9-18:75
69            android:name="com.capacitorjs.plugins.browser.BrowserControllerActivity"
69-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-85
70            android:exported="false"
70-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
71            android:launchMode="singleTask"
71-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-44
72            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
72-->[:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:13-72
73
74        <service
74-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-14:19
75            android:name="com.capacitorjs.plugins.pushnotifications.MessagingService"
75-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-86
76            android:exported="false" >
76-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
77            <intent-filter>
77-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-13:29
78                <action android:name="com.google.firebase.MESSAGING_EVENT" />
78-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:17-78
78-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:25-75
79            </intent-filter>
80        </service>
81
82        <receiver
82-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:29:9-40:20
83            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
83-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:30:13-78
84            android:exported="true"
84-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:31:13-36
85            android:permission="com.google.android.c2dm.permission.SEND" >
85-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:32:13-73
86            <intent-filter>
86-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:33:13-35:29
87                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
87-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:34:17-81
87-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:34:25-78
88            </intent-filter>
89
90            <meta-data
90-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:37:13-39:40
91                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
91-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:38:17-92
92                android:value="true" />
92-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:39:17-37
93        </receiver>
94        <!--
95             FirebaseMessagingService performs security checks at runtime,
96             but set to not exported to explicitly avoid allowing another app to call it.
97        -->
98        <service
98-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:46:9-53:19
99            android:name="com.google.firebase.messaging.FirebaseMessagingService"
99-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:47:13-82
100            android:directBootAware="true"
100-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:48:13-43
101            android:exported="false" >
101-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:49:13-37
102            <intent-filter android:priority="-500" >
102-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-13:29
103                <action android:name="com.google.firebase.MESSAGING_EVENT" />
103-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:17-78
103-->[:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:25-75
104            </intent-filter>
105        </service>
106        <service
106-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:54:9-63:19
107            android:name="com.google.firebase.components.ComponentDiscoveryService"
107-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:55:13-84
108            android:directBootAware="true"
108-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:32:13-43
109            android:exported="false" >
109-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:56:13-37
110            <meta-data
110-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:57:13-59:85
111                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
111-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:58:17-122
112                android:value="com.google.firebase.components.ComponentRegistrar" />
112-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:59:17-82
113            <meta-data
113-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:60:13-62:85
114                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
114-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:61:17-119
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:62:17-82
116            <meta-data
116-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:15:13-17:85
117                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
117-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:16:17-130
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:17:17-82
119            <meta-data
119-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:18:13-20:85
120                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
120-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:19:17-127
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:20:17-82
122            <meta-data
122-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/324fc306ed84dc357040da54cc5f1fbc/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
123                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
123-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/324fc306ed84dc357040da54cc5f1fbc/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/324fc306ed84dc357040da54cc5f1fbc/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
125            <meta-data
125-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
126                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
126-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:36:17-109
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:37:17-82
128            <meta-data
128-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
129                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
129-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
131        </service>
132
133        <activity
133-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:9-173
134            android:name="com.google.android.gms.common.api.GoogleApiActivity"
134-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:19-85
135            android:exported="false"
135-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:146-170
136            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
136-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:86-145
137
138        <provider
138-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
139            android:name="com.google.firebase.provider.FirebaseInitProvider"
139-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:24:13-77
140            android:authorities="my.supershoppe.app.firebaseinitprovider"
140-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:25:13-72
141            android:directBootAware="true"
141-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:26:13-43
142            android:exported="false"
142-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:27:13-37
143            android:initOrder="100" />
143-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:28:13-36
144        <provider
144-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
145            android:name="androidx.startup.InitializationProvider"
145-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
146            android:authorities="my.supershoppe.app.androidx-startup"
146-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
147            android:exported="false" >
147-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
148            <meta-data
148-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
149                android:name="androidx.emoji2.text.EmojiCompatInitializer"
149-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
150                android:value="androidx.startup" />
150-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
151            <meta-data
151-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/2086035d7e747a32c2be40e6ed7f404e/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
152                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
152-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/2086035d7e747a32c2be40e6ed7f404e/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
153                android:value="androidx.startup" />
153-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/2086035d7e747a32c2be40e6ed7f404e/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
154            <meta-data
154-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
155                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
155-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
156                android:value="androidx.startup" />
156-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
157        </provider>
158
159        <meta-data
159-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
160            android:name="com.google.android.gms.version"
160-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:20-65
161            android:value="@integer/google_play_services_version" />
161-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:66-119
162
163        <receiver
163-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
164            android:name="androidx.profileinstaller.ProfileInstallReceiver"
164-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
165            android:directBootAware="false"
165-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
166            android:enabled="true"
166-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
167            android:exported="true"
167-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
168            android:permission="android.permission.DUMP" >
168-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
169            <intent-filter>
169-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
170                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
170-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
170-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
171            </intent-filter>
172            <intent-filter>
172-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
173                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
173-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
173-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
174            </intent-filter>
175            <intent-filter>
175-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
176                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
176-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
176-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
177            </intent-filter>
178            <intent-filter>
178-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
179                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
179-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
179-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
180            </intent-filter>
181        </receiver>
182
183        <service
183-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
184            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
184-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
185            android:exported="false" >
185-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
186            <meta-data
186-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
187                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
187-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
188                android:value="cct" />
188-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
189        </service>
190        <service
190-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
191            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
191-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
192            android:exported="false"
192-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
193            android:permission="android.permission.BIND_JOB_SERVICE" >
193-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
194        </service>
195
196        <receiver
196-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
197            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
197-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
198            android:exported="false" />
198-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
199    </application>
200
201</manifest>
