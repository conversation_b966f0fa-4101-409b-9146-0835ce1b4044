-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:27:9-35:20
	android:grantUriPermissions
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:31:13-47
	android:authorities
		INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:29:13-64
	android:exported
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:30:13-37
	android:name
		<PERSON><PERSON><PERSON> from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:28:13-62
manifest
ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:2:1-41:12
INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:2:1-41:12
INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:2:1-41:12
INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:2:1-41:12
MERGED from [:capacitor-community-privacy-screen] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor-community/privacy-screen/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-community-screen-brightness] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor-community/screen-brightness/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-app] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/app/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-barcode-scanner] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/barcode-scanner/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-21:12
MERGED from [:capacitor-device] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/device/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-geolocation] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/geolocation/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-preferences] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/preferences/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-17:12
MERGED from [:capacitor-toast] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/toast/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-android] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/android/capacitor/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-cordova-android-plugins] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/16884767c054ac4cab0f70a5a4855d4d/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-video:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4efc6a0d3ac3519ece2ebc5e9687b4d7/transformed/camera-video-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-core:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7f66536c5c1ee456d59e4d79dbad951/transformed/camera-core-1.4.0/AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-lifecycle:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/75beaa5a30231030064fb8116fedd9dd/transformed/camera-lifecycle-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ddf9110bf974cf4ff4f36020e3a3d166/transformed/camera-camera2-1.4.0/AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/211afc976588db4fa106afcb827907e0/transformed/camera-view-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/e9453dd7c24b4e56e933c53d98771b29/transformed/barcode-scanning-17.3.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f5c910be0583abc17910421310df0d/transformed/play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/c2035d4d29dfc874381a9b93e3909d27/transformed/barcode-scanning-common-17.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e0ce33f42f27f702f4ff85a02894c3e/transformed/vision-common-17.3.0/AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:2:1-26:12
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/dda665aa4a1576cfb1759fb2bbcd5279/transformed/appcompat-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/75881b531e34911967ea794bd3408c30/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-splashscreen:1.0.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/ef234481c09f01fb9f0508a5da2b1126/transformed/core-splashscreen-1.0.1/AndroidManifest.xml:17:1-24:12
MERGED from [io.ionic.libs:iongeolocation-android:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/cf5c25e299eb52ddcbcc73083ca78071/transformed/iongeolocation-android-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/87382da045b5faebd5371143916fd9ef/transformed/play-services-location-21.3.0/AndroidManifest.xml:2:1-8:12
MERGED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/324fc306ed84dc357040da54cc5f1fbc/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b70f94d043134f6a6c82aff3176808d5/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f872c9901e2c95f57da6f5d814a1f18d/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:2:1-13:12
MERGED from [androidx.compose.material3:material3-android:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/84189f789b5e554413b94a7106c44443/transformed/material3-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/473eb56de8fdde354746a00f2b212e79/transformed/material3-window-size-class-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/af63697f8d71de32bb865ead3b16e840/transformed/material-icons-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/84041e628ca06b56b0c367bfe2689489/transformed/material-ripple-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1f89ca704025627e4d13e54c70937878/transformed/animation-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5b48ff3098830dab0b16939714fdea2e/transformed/animation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/c188b1f33ec69013f685110930c34ac1/transformed/foundation-layout-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/80c866390182a769f8356a80ded4035a/transformed/foundation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/84de9b59766eaa32d6d619568e74bcbe/transformed/ui-geometry-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/be876e59c13dd89f012a4dfee77ada4d/transformed/ui-text-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1752430dcf0de4b1c1fddcd7c40ca3c4/transformed/ui-graphics-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/12848fe6cdd21cfa7159ab8af72bd862/transformed/ui-util-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f4fad70e94b91df7b97a692ceda40a6b/transformed/ui-unit-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a8dee9b18ef2037d25f00c7473086895/transformed/ui-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/6c8f2b475cbb949dc656efeb2abd4fcd/transformed/activity-ktx-1.9.3/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/6ffe41055c7c7f8b8c2ec13b75c16de9/transformed/activity-compose-1.9.3/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5da5a92e768a9ab7ef559eb601b1935e/transformed/runtime-saveable-release/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/509d7e86b27aaa293843dd95b79d84c3/transformed/play-services-stats-17.0.2/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/206ff51956c4746556b0b1a85c73686b/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/111c5c3c3f6ae179fe7713443d447c72/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fd457bbc543ecdbee5c91b5aa5e11e77/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d222fc1537ba4d05d2594c911ab56ac7/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/17f9b79c3605804ded7cffe50acaf238/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/9f8069ce470fc0e16369d43e56e54e22/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/3fbbd497413c0ee4069f169f4dc7a260/transformed/webkit-1.12.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ce7a7439fda0c36e77102a7d38ff3226/transformed/browser-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/243233694e4ca3136f1ca4dacc434c39/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/bf2bdf699ee56f42fbfc2f03618abe36/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fbae0b886760a9e76e330106e8598bdf/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/eaa603a2c27a5645906131bea5891cf1/transformed/core-ktx-1.15.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1a5e33b0d3052789160d3f33fbe3bf9e/transformed/autofill-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/1e57f61b1ff1a00797ea612917a26db3/transformed/graphics-path-1.0.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/f974b30b07e5bde53cb3618bce731f69/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/998c918bf96ae2f6a4f5c8c644413a6f/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/0a5e505c75e784ae05520192697d10a3/transformed/lifecycle-viewmodel-2.8.3/AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/50f3617f2a68b81da53c423be34739c9/transformed/lifecycle-viewmodel-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/2902ea0ba24ff96cbd9fcd3b9db16fc8/transformed/lifecycle-livedata-core-ktx-2.8.3/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/1f4a0e54a64832941dd84ec9cce70b6e/transformed/lifecycle-runtime-ktx-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/73dcf6b3a582150357036810e8cb6e2f/transformed/lifecycle-runtime-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/29517d4c72d56af8a31a59b252b79c42/transformed/lifecycle-viewmodel-ktx-2.8.3/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/8a600a303545e72802df52a736763206/transformed/lifecycle-viewmodel-savedstate-2.8.3/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/2aa292f5dbed1ed8a4c314673eb3a237/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/621ba428d3c19163584ca6f92bc021fc/transformed/lifecycle-livedata-core-2.8.3/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/8b64692e0eb74e20b781814a0b835415/transformed/lifecycle-livedata-2.8.3/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/4ff39605624832b079af437726b94c3a/transformed/lifecycle-runtime-compose-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8d552b8391ba12643bb2535ec374a7ed/transformed/runtime-release/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0faac3f3b355a42cd6662bd6d8ec2a3f/transformed/vision-interfaces-16.3.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/6fd50506c236bde2540ace785bc60713/transformed/firebase-installations-interop-17.1.1/AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/19f28f4dfdbce1cf5b5d150bff07fa96/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3cd5cde5baf76ffd76ccca9ac0ba6533/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.8.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/7a4193c6fbbe5e128015b7f6283124c0/transformed/fragment-1.8.4/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/d8c531c2afb462bf72eed5fcccc5521b/transformed/activity-1.9.3/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/60fa4356081104865d164e2dc1a44707/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0c35e3b0c2fe34519a603108fedf6f64/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/c246ddff93723fa14095088b03472f98/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d0dd69b97879592427f95ffc5135b708/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/640934fa0c2deb02232fb188bbe4540c/transformed/tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/83cc729ebb343678ef420dbf99d69098/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/27b18f4155bc5ffa642e6d94ad680607/transformed/firebase-components-18.0.0/AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f96d669de42549aba9c948d9b3d391/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/70b2f935023aef227fa195183aee45e2/transformed/transport-api-3.1.0/AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8f584d735ff9bfd7953105e9d7907cfc/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/4b591080b92678bdb8219377ca4051fe/transformed/exifinterface-1.3.2/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/67accc8724fe27e8ee25560dda61f8a8/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d911d07782f4978bb71e91acc0c48400/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b6b333b6497d110c82f46d53344a04f8/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/c7519e99783a8412739804580db52b13/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:2:1-7:12
MERGED from [org.apache.cordova:framework:10.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/1684f9ee58db97dc3c9598ebbc48c3f5/transformed/framework-10.1.1/AndroidManifest.xml:20:1-27:12
MERGED from [io.ionic.libs:ionbarcode-android:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7bf83b626d16f9a93e391c001ed93a7/transformed/ionbarcode-android-1.2.0/AndroidManifest.xml:2:1-15:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] /Users/<USER>/.gradle/caches/8.11.1/transforms/18079f4cd0af989d38f9299f94910944/transformed/image-1.0.0-beta1/AndroidManifest.xml:2:1-9:12
	package
		INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:2:11-69
application
ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:4:5-36:19
INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:4:5-36:19
MERGED from [:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:5-19:19
MERGED from [:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:5-19:19
MERGED from [:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-15:19
MERGED from [:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-15:19
MERGED from [:capacitor-cordova-android-plugins] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-9:19
MERGED from [:capacitor-cordova-android-plugins] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-9:19
MERGED from [androidx.camera:camera-core:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7f66536c5c1ee456d59e4d79dbad951/transformed/camera-core-1.4.0/AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7f66536c5c1ee456d59e4d79dbad951/transformed/camera-core-1.4.0/AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ddf9110bf974cf4ff4f36020e3a3d166/transformed/camera-camera2-1.4.0/AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ddf9110bf974cf4ff4f36020e3a3d166/transformed/camera-camera2-1.4.0/AndroidManifest.xml:23:5-34:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f5c910be0583abc17910421310df0d/transformed/play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f5c910be0583abc17910421310df0d/transformed/play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e0ce33f42f27f702f4ff85a02894c3e/transformed/vision-common-17.3.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e0ce33f42f27f702f4ff85a02894c3e/transformed/vision-common-17.3.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/87382da045b5faebd5371143916fd9ef/transformed/play-services-location-21.3.0/AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/87382da045b5faebd5371143916fd9ef/transformed/play-services-location-21.3.0/AndroidManifest.xml:6:5-20
MERGED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/324fc306ed84dc357040da54cc5f1fbc/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/324fc306ed84dc357040da54cc5f1fbc/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b70f94d043134f6a6c82aff3176808d5/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b70f94d043134f6a6c82aff3176808d5/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/AndroidManifest.xml:24:5-31:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/509d7e86b27aaa293843dd95b79d84c3/transformed/play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/509d7e86b27aaa293843dd95b79d84c3/transformed/play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/2aa292f5dbed1ed8a4c314673eb3a237/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/2aa292f5dbed1ed8a4c314673eb3a237/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/19f28f4dfdbce1cf5b5d150bff07fa96/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/19f28f4dfdbce1cf5b5d150bff07fa96/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3cd5cde5baf76ffd76ccca9ac0ba6533/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3cd5cde5baf76ffd76ccca9ac0ba6533/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0c35e3b0c2fe34519a603108fedf6f64/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0c35e3b0c2fe34519a603108fedf6f64/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/83cc729ebb343678ef420dbf99d69098/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/83cc729ebb343678ef420dbf99d69098/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
MERGED from [io.ionic.libs:ionbarcode-android:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7bf83b626d16f9a93e391c001ed93a7/transformed/ionbarcode-android-1.2.0/AndroidManifest.xml:9:5-13:19
MERGED from [io.ionic.libs:ionbarcode-android:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7bf83b626d16f9a93e391c001ed93a7/transformed/ionbarcode-android-1.2.0/AndroidManifest.xml:9:5-13:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] /Users/<USER>/.gradle/caches/8.11.1/transforms/18079f4cd0af989d38f9299f94910944/transformed/image-1.0.0-beta1/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] /Users/<USER>/.gradle/caches/8.11.1/transforms/18079f4cd0af989d38f9299f94910944/transformed/image-1.0.0-beta1/AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:9:9-35
	android:label
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:7:9-41
	android:roundIcon
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:8:9-54
	android:icon
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:6:9-43
	android:allowBackup
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:5:9-35
	android:theme
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:10:9-40
activity#my.supershoppe.app.MainActivity
ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:12:9-25:20
	android:label
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:15:13-56
	android:launchMode
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:17:13-44
	android:exported
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:18:13-36
	android:configChanges
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:13:13-140
	android:theme
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:16:13-62
	android:name
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:14:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:20:13-23:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:21:17-69
	android:name
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:21:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:22:17-77
	android:name
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:22:27-74
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:40:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f872c9901e2c95f57da6f5d814a1f18d/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f872c9901e2c95f57da6f5d814a1f18d/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
	android:name
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:40:22-64
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:32:13-34:64
	android:resource
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:34:17-51
	android:name
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml:33:17-67
uses-sdk
INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml
MERGED from [:capacitor-community-privacy-screen] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor-community/privacy-screen/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-community-privacy-screen] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor-community/privacy-screen/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-community-screen-brightness] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor-community/screen-brightness/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-community-screen-brightness] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor-community/screen-brightness/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-app] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/app/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-app] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/app/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-barcode-scanner] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/barcode-scanner/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-barcode-scanner] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/barcode-scanner/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-device] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/device/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-device] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/device/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-geolocation] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/geolocation/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-geolocation] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/geolocation/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-preferences] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/preferences/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-preferences] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/preferences/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-toast] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/toast/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-toast] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/toast/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-android] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/android/capacitor/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-android] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/android/capacitor/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:capacitor-cordova-android-plugins] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:capacitor-cordova-android-plugins] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/16884767c054ac4cab0f70a5a4855d4d/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/16884767c054ac4cab0f70a5a4855d4d/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4efc6a0d3ac3519ece2ebc5e9687b4d7/transformed/camera-video-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4efc6a0d3ac3519ece2ebc5e9687b4d7/transformed/camera-video-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-core:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7f66536c5c1ee456d59e4d79dbad951/transformed/camera-core-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7f66536c5c1ee456d59e4d79dbad951/transformed/camera-core-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/75beaa5a30231030064fb8116fedd9dd/transformed/camera-lifecycle-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/75beaa5a30231030064fb8116fedd9dd/transformed/camera-lifecycle-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ddf9110bf974cf4ff4f36020e3a3d166/transformed/camera-camera2-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ddf9110bf974cf4ff4f36020e3a3d166/transformed/camera-camera2-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/211afc976588db4fa106afcb827907e0/transformed/camera-view-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/211afc976588db4fa106afcb827907e0/transformed/camera-view-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/e9453dd7c24b4e56e933c53d98771b29/transformed/barcode-scanning-17.3.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/e9453dd7c24b4e56e933c53d98771b29/transformed/barcode-scanning-17.3.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f5c910be0583abc17910421310df0d/transformed/play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f5c910be0583abc17910421310df0d/transformed/play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/c2035d4d29dfc874381a9b93e3909d27/transformed/barcode-scanning-common-17.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/c2035d4d29dfc874381a9b93e3909d27/transformed/barcode-scanning-common-17.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e0ce33f42f27f702f4ff85a02894c3e/transformed/vision-common-17.3.0/AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e0ce33f42f27f702f4ff85a02894c3e/transformed/vision-common-17.3.0/AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:6:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/dda665aa4a1576cfb1759fb2bbcd5279/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/dda665aa4a1576cfb1759fb2bbcd5279/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/75881b531e34911967ea794bd3408c30/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/75881b531e34911967ea794bd3408c30/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/ef234481c09f01fb9f0508a5da2b1126/transformed/core-splashscreen-1.0.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/ef234481c09f01fb9f0508a5da2b1126/transformed/core-splashscreen-1.0.1/AndroidManifest.xml:20:5-22:41
MERGED from [io.ionic.libs:iongeolocation-android:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/cf5c25e299eb52ddcbcc73083ca78071/transformed/iongeolocation-android-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [io.ionic.libs:iongeolocation-android:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/cf5c25e299eb52ddcbcc73083ca78071/transformed/iongeolocation-android-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/87382da045b5faebd5371143916fd9ef/transformed/play-services-location-21.3.0/AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/87382da045b5faebd5371143916fd9ef/transformed/play-services-location-21.3.0/AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/324fc306ed84dc357040da54cc5f1fbc/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/324fc306ed84dc357040da54cc5f1fbc/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b70f94d043134f6a6c82aff3176808d5/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b70f94d043134f6a6c82aff3176808d5/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f872c9901e2c95f57da6f5d814a1f18d/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f872c9901e2c95f57da6f5d814a1f18d/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/84189f789b5e554413b94a7106c44443/transformed/material3-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/84189f789b5e554413b94a7106c44443/transformed/material3-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/473eb56de8fdde354746a00f2b212e79/transformed/material3-window-size-class-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/473eb56de8fdde354746a00f2b212e79/transformed/material3-window-size-class-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/af63697f8d71de32bb865ead3b16e840/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/af63697f8d71de32bb865ead3b16e840/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/84041e628ca06b56b0c367bfe2689489/transformed/material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/84041e628ca06b56b0c367bfe2689489/transformed/material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1f89ca704025627e4d13e54c70937878/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1f89ca704025627e4d13e54c70937878/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5b48ff3098830dab0b16939714fdea2e/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5b48ff3098830dab0b16939714fdea2e/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/c188b1f33ec69013f685110930c34ac1/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/c188b1f33ec69013f685110930c34ac1/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/80c866390182a769f8356a80ded4035a/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/80c866390182a769f8356a80ded4035a/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/84de9b59766eaa32d6d619568e74bcbe/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/84de9b59766eaa32d6d619568e74bcbe/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/be876e59c13dd89f012a4dfee77ada4d/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/be876e59c13dd89f012a4dfee77ada4d/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1752430dcf0de4b1c1fddcd7c40ca3c4/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1752430dcf0de4b1c1fddcd7c40ca3c4/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/12848fe6cdd21cfa7159ab8af72bd862/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/12848fe6cdd21cfa7159ab8af72bd862/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f4fad70e94b91df7b97a692ceda40a6b/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f4fad70e94b91df7b97a692ceda40a6b/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a8dee9b18ef2037d25f00c7473086895/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a8dee9b18ef2037d25f00c7473086895/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/6c8f2b475cbb949dc656efeb2abd4fcd/transformed/activity-ktx-1.9.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/6c8f2b475cbb949dc656efeb2abd4fcd/transformed/activity-ktx-1.9.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/6ffe41055c7c7f8b8c2ec13b75c16de9/transformed/activity-compose-1.9.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/6ffe41055c7c7f8b8c2ec13b75c16de9/transformed/activity-compose-1.9.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5da5a92e768a9ab7ef559eb601b1935e/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5da5a92e768a9ab7ef559eb601b1935e/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/509d7e86b27aaa293843dd95b79d84c3/transformed/play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/509d7e86b27aaa293843dd95b79d84c3/transformed/play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/206ff51956c4746556b0b1a85c73686b/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/206ff51956c4746556b0b1a85c73686b/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/111c5c3c3f6ae179fe7713443d447c72/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/111c5c3c3f6ae179fe7713443d447c72/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fd457bbc543ecdbee5c91b5aa5e11e77/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fd457bbc543ecdbee5c91b5aa5e11e77/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d222fc1537ba4d05d2594c911ab56ac7/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d222fc1537ba4d05d2594c911ab56ac7/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/17f9b79c3605804ded7cffe50acaf238/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/17f9b79c3605804ded7cffe50acaf238/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/9f8069ce470fc0e16369d43e56e54e22/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/9f8069ce470fc0e16369d43e56e54e22/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/3fbbd497413c0ee4069f169f4dc7a260/transformed/webkit-1.12.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/3fbbd497413c0ee4069f169f4dc7a260/transformed/webkit-1.12.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ce7a7439fda0c36e77102a7d38ff3226/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ce7a7439fda0c36e77102a7d38ff3226/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/243233694e4ca3136f1ca4dacc434c39/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/243233694e4ca3136f1ca4dacc434c39/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/bf2bdf699ee56f42fbfc2f03618abe36/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/bf2bdf699ee56f42fbfc2f03618abe36/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fbae0b886760a9e76e330106e8598bdf/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fbae0b886760a9e76e330106e8598bdf/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/eaa603a2c27a5645906131bea5891cf1/transformed/core-ktx-1.15.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/eaa603a2c27a5645906131bea5891cf1/transformed/core-ktx-1.15.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1a5e33b0d3052789160d3f33fbe3bf9e/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1a5e33b0d3052789160d3f33fbe3bf9e/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/1e57f61b1ff1a00797ea612917a26db3/transformed/graphics-path-1.0.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/1e57f61b1ff1a00797ea612917a26db3/transformed/graphics-path-1.0.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/f974b30b07e5bde53cb3618bce731f69/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/f974b30b07e5bde53cb3618bce731f69/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/998c918bf96ae2f6a4f5c8c644413a6f/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/998c918bf96ae2f6a4f5c8c644413a6f/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/0a5e505c75e784ae05520192697d10a3/transformed/lifecycle-viewmodel-2.8.3/AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/0a5e505c75e784ae05520192697d10a3/transformed/lifecycle-viewmodel-2.8.3/AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/50f3617f2a68b81da53c423be34739c9/transformed/lifecycle-viewmodel-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/50f3617f2a68b81da53c423be34739c9/transformed/lifecycle-viewmodel-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/2902ea0ba24ff96cbd9fcd3b9db16fc8/transformed/lifecycle-livedata-core-ktx-2.8.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/2902ea0ba24ff96cbd9fcd3b9db16fc8/transformed/lifecycle-livedata-core-ktx-2.8.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/1f4a0e54a64832941dd84ec9cce70b6e/transformed/lifecycle-runtime-ktx-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/1f4a0e54a64832941dd84ec9cce70b6e/transformed/lifecycle-runtime-ktx-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/73dcf6b3a582150357036810e8cb6e2f/transformed/lifecycle-runtime-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/73dcf6b3a582150357036810e8cb6e2f/transformed/lifecycle-runtime-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/29517d4c72d56af8a31a59b252b79c42/transformed/lifecycle-viewmodel-ktx-2.8.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/29517d4c72d56af8a31a59b252b79c42/transformed/lifecycle-viewmodel-ktx-2.8.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/8a600a303545e72802df52a736763206/transformed/lifecycle-viewmodel-savedstate-2.8.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/8a600a303545e72802df52a736763206/transformed/lifecycle-viewmodel-savedstate-2.8.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/2aa292f5dbed1ed8a4c314673eb3a237/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/2aa292f5dbed1ed8a4c314673eb3a237/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/621ba428d3c19163584ca6f92bc021fc/transformed/lifecycle-livedata-core-2.8.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/621ba428d3c19163584ca6f92bc021fc/transformed/lifecycle-livedata-core-2.8.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/8b64692e0eb74e20b781814a0b835415/transformed/lifecycle-livedata-2.8.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/8b64692e0eb74e20b781814a0b835415/transformed/lifecycle-livedata-2.8.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/4ff39605624832b079af437726b94c3a/transformed/lifecycle-runtime-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/4ff39605624832b079af437726b94c3a/transformed/lifecycle-runtime-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8d552b8391ba12643bb2535ec374a7ed/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8d552b8391ba12643bb2535ec374a7ed/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0faac3f3b355a42cd6662bd6d8ec2a3f/transformed/vision-interfaces-16.3.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0faac3f3b355a42cd6662bd6d8ec2a3f/transformed/vision-interfaces-16.3.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/6fd50506c236bde2540ace785bc60713/transformed/firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/6fd50506c236bde2540ace785bc60713/transformed/firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/19f28f4dfdbce1cf5b5d150bff07fa96/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/19f28f4dfdbce1cf5b5d150bff07fa96/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3cd5cde5baf76ffd76ccca9ac0ba6533/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3cd5cde5baf76ffd76ccca9ac0ba6533/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.8.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/7a4193c6fbbe5e128015b7f6283124c0/transformed/fragment-1.8.4/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/7a4193c6fbbe5e128015b7f6283124c0/transformed/fragment-1.8.4/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/d8c531c2afb462bf72eed5fcccc5521b/transformed/activity-1.9.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/d8c531c2afb462bf72eed5fcccc5521b/transformed/activity-1.9.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/60fa4356081104865d164e2dc1a44707/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/60fa4356081104865d164e2dc1a44707/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0c35e3b0c2fe34519a603108fedf6f64/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0c35e3b0c2fe34519a603108fedf6f64/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/c246ddff93723fa14095088b03472f98/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/c246ddff93723fa14095088b03472f98/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d0dd69b97879592427f95ffc5135b708/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d0dd69b97879592427f95ffc5135b708/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/640934fa0c2deb02232fb188bbe4540c/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/640934fa0c2deb02232fb188bbe4540c/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/83cc729ebb343678ef420dbf99d69098/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/83cc729ebb343678ef420dbf99d69098/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/27b18f4155bc5ffa642e6d94ad680607/transformed/firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/27b18f4155bc5ffa642e6d94ad680607/transformed/firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f96d669de42549aba9c948d9b3d391/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f96d669de42549aba9c948d9b3d391/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/70b2f935023aef227fa195183aee45e2/transformed/transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/70b2f935023aef227fa195183aee45e2/transformed/transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8f584d735ff9bfd7953105e9d7907cfc/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8f584d735ff9bfd7953105e9d7907cfc/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/4b591080b92678bdb8219377ca4051fe/transformed/exifinterface-1.3.2/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/4b591080b92678bdb8219377ca4051fe/transformed/exifinterface-1.3.2/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/67accc8724fe27e8ee25560dda61f8a8/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/67accc8724fe27e8ee25560dda61f8a8/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d911d07782f4978bb71e91acc0c48400/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d911d07782f4978bb71e91acc0c48400/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b6b333b6497d110c82f46d53344a04f8/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b6b333b6497d110c82f46d53344a04f8/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/c7519e99783a8412739804580db52b13/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/c7519e99783a8412739804580db52b13/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [org.apache.cordova:framework:10.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/1684f9ee58db97dc3c9598ebbc48c3f5/transformed/framework-10.1.1/AndroidManifest.xml:25:5-44
MERGED from [org.apache.cordova:framework:10.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/1684f9ee58db97dc3c9598ebbc48c3f5/transformed/framework-10.1.1/AndroidManifest.xml:25:5-44
MERGED from [io.ionic.libs:ionbarcode-android:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7bf83b626d16f9a93e391c001ed93a7/transformed/ionbarcode-android-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [io.ionic.libs:ionbarcode-android:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7bf83b626d16f9a93e391c001ed93a7/transformed/ionbarcode-android-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] /Users/<USER>/.gradle/caches/8.11.1/transforms/18079f4cd0af989d38f9299f94910944/transformed/image-1.0.0-beta1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] /Users/<USER>/.gradle/caches/8.11.1/transforms/18079f4cd0af989d38f9299f94910944/transformed/image-1.0.0-beta1/AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/app/src/main/AndroidManifest.xml
queries
ADDED from [:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-11:15
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-10:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-90
	android:name
		ADDED from [:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:21-87
activity#com.capacitorjs.plugins.browser.BrowserControllerActivity
ADDED from [:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:9-18:75
	android:launchMode
		ADDED from [:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-44
	android:exported
		ADDED from [:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:13-72
	android:name
		ADDED from [:capacitor-browser] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-85
service#com.capacitorjs.plugins.pushnotifications.MessagingService
ADDED from [:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-14:19
	android:exported
		ADDED from [:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
	android:name
		ADDED from [:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-86
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-13:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:17-78
	android:name
		ADDED from [:capacitor-push-notifications] /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/node_modules/@capacitor/push-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:25-75
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-core:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7f66536c5c1ee456d59e4d79dbad951/transformed/camera-core-1.4.0/AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-camera2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ddf9110bf974cf4ff4f36020e3a3d166/transformed/camera-camera2-1.4.0/AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-camera2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ddf9110bf974cf4ff4f36020e3a3d166/transformed/camera-camera2-1.4.0/AndroidManifest.xml:24:9-33:19
	android:enabled
		ADDED from [androidx.camera:camera-core:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7f66536c5c1ee456d59e4d79dbad951/transformed/camera-core-1.4.0/AndroidManifest.xml:31:13-36
	android:exported
		ADDED from [androidx.camera:camera-core:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7f66536c5c1ee456d59e4d79dbad951/transformed/camera-core-1.4.0/AndroidManifest.xml:32:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-core:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7f66536c5c1ee456d59e4d79dbad951/transformed/camera-core-1.4.0/AndroidManifest.xml:33:13-75
	android:name
		ADDED from [androidx.camera:camera-core:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7f66536c5c1ee456d59e4d79dbad951/transformed/camera-core-1.4.0/AndroidManifest.xml:30:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ddf9110bf974cf4ff4f36020e3a3d166/transformed/camera-camera2-1.4.0/AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ddf9110bf974cf4ff4f36020e3a3d166/transformed/camera-camera2-1.4.0/AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ddf9110bf974cf4ff4f36020e3a3d166/transformed/camera-camera2-1.4.0/AndroidManifest.xml:31:17-103
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f5c910be0583abc17910421310df0d/transformed/play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e0ce33f42f27f702f4ff85a02894c3e/transformed/vision-common-17.3.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e0ce33f42f27f702f4ff85a02894c3e/transformed/vision-common-17.3.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f5c910be0583abc17910421310df0d/transformed/play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f5c910be0583abc17910421310df0d/transformed/play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f5c910be0583abc17910421310df0d/transformed/play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f5c910be0583abc17910421310df0d/transformed/play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/98f5c910be0583abc17910421310df0d/transformed/play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e0ce33f42f27f702f4ff85a02894c3e/transformed/vision-common-17.3.0/AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e0ce33f42f27f702f4ff85a02894c3e/transformed/vision-common-17.3.0/AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e0ce33f42f27f702f4ff85a02894c3e/transformed/vision-common-17.3.0/AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/00e58ea1cc4a0af26243a111c4c6d76e/transformed/common-18.11.0/AndroidManifest.xml:21:17-120
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f872c9901e2c95f57da6f5d814a1f18d/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f872c9901e2c95f57da6f5d814a1f18d/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:22:22-76
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:23:5-77
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:23:22-74
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f872c9901e2c95f57da6f5d814a1f18d/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f872c9901e2c95f57da6f5d814a1f18d/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:24:22-65
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f872c9901e2c95f57da6f5d814a1f18d/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f872c9901e2c95f57da6f5d814a1f18d/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/324fc306ed84dc357040da54cc5f1fbc/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/324fc306ed84dc357040da54cc5f1fbc/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2c04d43294058e70b8ad79d5184e7401/transformed/firebase-messaging-24.1.0/AndroidManifest.xml:61:17-119
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3897ee7a3a7e64eb47ff9b7bb8256b24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:19-85
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d121929483b8667b8bd7f522bf5e661/transformed/firebase-installations-17.2.0/AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/324fc306ed84dc357040da54cc5f1fbc/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/324fc306ed84dc357040da54cc5f1fbc/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/324fc306ed84dc357040da54cc5f1fbc/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f546ee03c2308c684eeebc8faf0e1407/transformed/firebase-common-21.0.0/AndroidManifest.xml:36:17-109
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b1228b5b4280c65f74eddcb791a9d17/transformed/window-1.0.0/AndroidManifest.xml:29:13-51
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/2aa292f5dbed1ed8a4c314673eb3a237/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/2aa292f5dbed1ed8a4c314673eb3a237/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0c35e3b0c2fe34519a603108fedf6f64/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0c35e3b0c2fe34519a603108fedf6f64/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:23:9-81
permission#my.supershoppe.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:26:22-94
uses-permission#my.supershoppe.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/2aa292f5dbed1ed8a4c314673eb3a237/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/2aa292f5dbed1ed8a4c314673eb3a237/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/2aa292f5dbed1ed8a4c314673eb3a237/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/697a983ff8b6be23efe7df3e3bbc5a94/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:20-65
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/c53ce620ea6072f75d375d7efaf4f97b/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.11.1/transforms/e166bb1d6f54168e00acc5493c1d998b/transformed/transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
uses-permission#android.permission.CAMERA
ADDED from [io.ionic.libs:ionbarcode-android:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7bf83b626d16f9a93e391c001ed93a7/transformed/ionbarcode-android-1.2.0/AndroidManifest.xml:7:5-65
	android:name
		ADDED from [io.ionic.libs:ionbarcode-android:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7bf83b626d16f9a93e391c001ed93a7/transformed/ionbarcode-android-1.2.0/AndroidManifest.xml:7:22-62
activity#com.outsystems.plugins.barcode.view.OSBARCScannerActivity
ADDED from [io.ionic.libs:ionbarcode-android:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7bf83b626d16f9a93e391c001ed93a7/transformed/ionbarcode-android-1.2.0/AndroidManifest.xml:10:9-12:40
	android:exported
		ADDED from [io.ionic.libs:ionbarcode-android:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7bf83b626d16f9a93e391c001ed93a7/transformed/ionbarcode-android-1.2.0/AndroidManifest.xml:12:13-37
	android:name
		ADDED from [io.ionic.libs:ionbarcode-android:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a7bf83b626d16f9a93e391c001ed93a7/transformed/ionbarcode-android-1.2.0/AndroidManifest.xml:11:13-85
