// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_21
      targetCompatibility JavaVersion.VERSION_21
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-community-privacy-screen')
    implementation project(':capacitor-community-screen-brightness')
    implementation project(':capacitor-app')
    implementation project(':capacitor-barcode-scanner')
    implementation project(':capacitor-browser')
    implementation project(':capacitor-device')
    implementation project(':capacitor-geolocation')
    implementation project(':capacitor-preferences')
    implementation project(':capacitor-push-notifications')
    implementation project(':capacitor-toast')

}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
