import { CNFlag } from '@components/icons/language/CNFlag';
import { USFlag } from '@components/icons/language/USFlag';
import { CURRENCY_ENUM } from '@framework/types';
import { ROUTES } from '@utils/routes';

export const siteSettings = {
  name: 'Super Shoppe Middleware',
  description: 'Super Shoppe Middleware Application',
  author: {
    name: 'super-shoppe-middleware',
    websiteUrl: 'https://supershoppe.my/',
    supportEmail: '<EMAIL>',
    address:
      'A-1-1, Block A, Taipan 2, Ara Damansara, Jalan PJU 1A/3, 47301, Petaling Jaya, Selangor.',
  },
  logo: {
    url: '/assets/images/logo.png',
    alt: 'Shop',
    href: '/',
    width: 128,
    height: 48,
  },
  reward_point_icon: '/assets/images/kk-coin.png',
  defaultLanguage: 'en',
  currencyCode: CURRENCY_ENUM.MY,
  site_header: {
    menu: [
      {
        id: 1,
        path: ROUTES.HOME,
        label: 'menu.menu-home',
      },
      {
        id: 2,
        path: '/',
        label: 'menu.menu-my-account',
        subMenu: [
          {
            id: 1,
            path: ROUTES.ACCOUNT,
            label: 'menu.menu-profile',
          },
          {
            id: 2,
            path: ROUTES.ORDERS,
            label: 'menu.menu-orders',
          },
          {
            id: 3,
            path: ROUTES.ADDRESS,
            label: 'menu.menu-address-book',
          },
          {
            id: 4,
            path: ROUTES.WISHLIST,
            label: 'menu.menu-wishlist',
          },
        ],
        requiredAuthorized: true,
      },
      // {
      //   id: 3,
      //   path: ROUTES.FAQ,
      //   label: 'menu.menu-faq',
      // },
      // {
      //   id: 4,
      //   path: ROUTES.ABOUT,
      //   label: 'menu.menu-about-us',
      // },
      // {
      //   id: 5,
      //   path: ROUTES.PRIVACY_POLICY,
      //   label: 'menu.menu-privacy-policy',
      // },
      // {
      //   id: 6,
      //   path: ROUTES.TERMS,
      //   label: 'menu.menu-terms-conditions',
      // },
      // {
      //   id: 7,
      //   path: ROUTES.DELIVERY_POLICY,
      //   label: 'menu.menu-delivery-policy',
      // },
      // {
      //   id: 8,
      //   path: ROUTES.RETURN_POLICY,
      //   label: 'menu.menu-return-policy',
      // },
      // {
      //   id: 9,
      //   path: ROUTES.CONTACT,
      //   label: 'menu.menu-contact-us',
      // },
      {
        id: 10,
        path: ROUTES.STORES,
        label: 'menu.menu-stores',
      },
    ],
    userAuthLinks: [
      {
        id: 10,
        path: ROUTES.LOGIN,
        label: 'menu.menu-sign-in',
        requiredAuthorized: false,
        hideInWebView: true,
      },
      {
        id: 11,
        path: ROUTES.SIGN_UP,
        label: 'menu.menu-sign-up',
        requiredAuthorized: false,
        hideInWebView: true,
      },
      {
        id: 12,
        path: ROUTES.LOGOUT,
        label: 'menu.menu-logout',
        requiredAuthorized: true,
        hideInWebView: true,
      },
    ],
    authorizedLinks: [
      { href: ROUTES.ACCOUNT, label: 'menu-my-account', title: 'My Account' },
      {
        href: ROUTES.LOGOUT,
        label: 'menu-logout',
        title: 'Logout',
        hideInWebView: true,
      },
    ],
    languageMenu: [
      {
        id: 'en',
        name: 'English - EN',
        value: 'en',
        icon: <USFlag />,
      },
      {
        id: 'zh',
        name: '中文 - ZH',
        value: 'zh',
        icon: <CNFlag />,
      },
    ],
  },
};
