import React, { useState, useRef, useEffect } from 'react';
import { useCurrentUser } from '../hooks/use-current-user';
import { CapacitorBarcodeScanner } from '@capacitor/barcode-scanner';

interface ScanHistoryItem {
  id: number;
  code: string;
  product: string;
  time: string;
  type: 'barcode' | 'qr';
}

const ScanPage = () => {
  const { user, isLoading } = useCurrentUser();
  const [isScanning, setIsScanning] = useState(false);
  const [scannedCode, setScannedCode] = useState('');
  const [scanHistory, setScanHistory] = useState<ScanHistoryItem[]>([
    {
      id: 1,
      code: '1234567890123',
      product: 'Apple iPhone 15',
      time: '10:30 AM',
      type: 'barcode',
    },
    {
      id: 2,
      code: 'QR-ABC123',
      product: 'Product Info',
      time: '10:25 AM',
      type: 'qr',
    },
    {
      id: 3,
      code: '9876543210987',
      product: 'Samsung Galaxy S24',
      time: '10:20 AM',
      type: 'barcode',
    },
  ]);
  const [error, setError] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize scanner on component mount
  useEffect(() => {
    // Any initialization logic can go here
    // For example, checking if the device supports barcode scanning
    return () => {
      // Cleanup if needed
    };
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <p className="text-red-600">No user data available.</p>
        </div>
      </div>
    );
  }

  // Check and request camera permissions
  const checkPermissions = async () => {
    try {
      // Note: In a real app, you might want to check permissions first
      // For now, we'll let the scanner handle permission requests
      return true;
    } catch (err) {
      console.error('Permission check error:', err);
      setError('Camera permission is required to scan barcodes.');
      return false;
    }
  };

  const handleStartScan = async () => {
    try {
      // Check permissions first
      const hasPermission = await checkPermissions();
      if (!hasPermission) {
        return;
      }

      setIsScanning(true);
      setError('');

      // Start the barcode scanner
      const result = await CapacitorBarcodeScanner.scanBarcode({
        hint: 17, // ALL - supports all barcode types
        scanInstructions: 'Point your camera at a barcode or QR code',
        scanButton: true,
        scanText: 'Scan',
        cameraDirection: 1, // BACK camera
        scanOrientation: 3, // ADAPTIVE
      });

      if (result.ScanResult) {
        setScannedCode(result.ScanResult);

        // Determine if it's a QR code or barcode based on content
        const isQR =
          result.ScanResult.includes('http') ||
          result.ScanResult.includes('://') ||
          result.ScanResult.length > 50;

        // Add to scan history
        const newScan: ScanHistoryItem = {
          id: scanHistory.length + 1,
          code: result.ScanResult,
          product: isQR ? 'QR Code Data' : 'Barcode Product',
          time: new Date().toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          }),
          type: isQR ? 'qr' : 'barcode',
        };
        setScanHistory([newScan, ...scanHistory]);
      }
    } catch (err) {
      console.error('Barcode scan error:', err);
      setError('Failed to scan barcode. Please try again.');
    } finally {
      setIsScanning(false);
    }
  };

  const handleManualInput = () => {
    const code = prompt('Enter barcode/QR code manually:');
    if (code) {
      setScannedCode(code);
      setError('');

      // Determine if it's a QR code or barcode based on content
      const isQR =
        code.includes('http') || code.includes('://') || code.length > 50;

      const newScan: ScanHistoryItem = {
        id: scanHistory.length + 1,
        code: code,
        product: 'Manual Entry',
        time: new Date().toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        }),
        type: isQR ? 'qr' : 'barcode',
      };
      setScanHistory([newScan, ...scanHistory]);
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Note: For a production app, you would need to implement image processing
      // to extract QR/barcode data from the uploaded image using libraries like
      // jsQR or QuaggaJS. For now, we'll simulate the scan.
      const mockCode = `IMG-${Date.now()}`;
      setScannedCode(mockCode);
      setError('');

      const newScan: ScanHistoryItem = {
        id: scanHistory.length + 1,
        code: mockCode,
        product: 'Image Scan',
        time: new Date().toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        }),
        type: 'qr',
      };
      setScanHistory([newScan, ...scanHistory]);
    }
  };

  const getTypeIcon = (type: string) => {
    if (type === 'qr') {
      return (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
          <path d="M3 11h8V3H3v8zm2-6h4v4H5V5zM3 21h8v-8H3v8zm2-6h4v4H5v-4zM13 3v8h8V3h-8zm6 6h-4V5h4v4zM19 13h2v2h-2zM13 13h2v2h-2zM15 15h2v2h-2zM13 17h2v2h-2zM15 19h2v2h-2zM17 17h2v2h-2zM19 19h2v2h-2zM17 15h2v2h-2z" />
        </svg>
      );
    }
    return (
      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
        <path d="M2 6h20v2H2zm0 4h20v1H2zm0 3h20v1H2zm0 3h20v1H2zm0 3h20v2H2z" />
      </svg>
    );
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Mobile Header */}
      <div className="bg-white shadow-sm px-4 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold text-gray-900">Scanner</h1>
          <button
            onClick={() => setScanHistory([])}
            className="text-red-600 text-sm font-medium"
          >
            Clear History
          </button>
        </div>
      </div>

      {/* Scanner Section */}
      <div className="px-4 py-6">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="text-center">
            {isScanning ? (
              <div className="py-12">
                <div className="mx-auto w-32 h-32 border-4 border-blue-600 border-dashed rounded-lg flex items-center justify-center mb-4 animate-pulse">
                  <svg
                    className="w-16 h-16 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"
                    />
                  </svg>
                </div>
                <p className="text-gray-600 mb-2">
                  Camera is active - scanning for codes...
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  Point your camera at a barcode or QR code
                </p>
                <button
                  onClick={() => setIsScanning(false)}
                  className="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition"
                >
                  Cancel Scan
                </button>
              </div>
            ) : (
              <div className="py-8">
                <div className="mx-auto w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <svg
                    className="w-12 h-12 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  QR & Barcode Scanner
                </h3>
                <p className="text-gray-600 mb-4">
                  Scan QR codes and barcodes to get product information
                </p>
                <div className="text-xs text-gray-500 mb-6 space-y-1">
                  <p>
                    • Supports all common barcode formats (UPC, EAN, Code 128,
                    etc.)
                  </p>
                  <p>• QR codes with URLs, text, and product data</p>
                  <p>• Camera permission required for scanning</p>
                </div>

                <div className="space-y-3">
                  <button
                    onClick={handleStartScan}
                    className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition font-medium"
                  >
                    Start Camera Scan
                  </button>

                  <div className="grid grid-cols-2 gap-3">
                    <button
                      onClick={handleManualInput}
                      className="bg-gray-100 text-gray-700 py-2 rounded-lg hover:bg-gray-200 transition text-sm"
                    >
                      Manual Input
                    </button>
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className="bg-gray-100 text-gray-700 py-2 rounded-lg hover:bg-gray-200 transition text-sm"
                    >
                      Upload Image
                    </button>
                  </div>
                </div>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
              </div>
            )}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-2 mb-2">
              <svg
                className="w-5 h-5 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <h4 className="font-medium text-red-800">Error</h4>
            </div>
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        {/* Last Scanned Result */}
        {scannedCode && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-2 mb-2">
              <svg
                className="w-5 h-5 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
              <h4 className="font-medium text-green-800">Last Scanned</h4>
            </div>
            <p className="text-green-700 font-mono text-sm">{scannedCode}</p>
          </div>
        )}

        {/* Scan History */}
        <div className="bg-white rounded-lg shadow-sm p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Scan History
          </h3>
          {scanHistory.length === 0 ? (
            <p className="text-gray-500 text-center py-4">No scans yet</p>
          ) : (
            <div className="space-y-3">
              {scanHistory.map((scan) => (
                <div
                  key={scan.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className={`p-2 rounded ${
                        scan.type === 'qr'
                          ? 'bg-blue-100 text-blue-600'
                          : 'bg-green-100 text-green-600'
                      }`}
                    >
                      {getTypeIcon(scan.type)}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">
                        {scan.product}
                      </p>
                      <p className="text-sm text-gray-600 font-mono">
                        {scan.code}
                      </p>
                    </div>
                  </div>
                  <span className="text-xs text-gray-500">{scan.time}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ScanPage;
