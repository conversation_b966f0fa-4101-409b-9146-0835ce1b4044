import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'my.supershoppe.middleware.app',
  appName: 'super-shoppe-middleware',
  webDir: 'out',
  // for development
  server: {
    url: 'http://192.168.0.120:3000',
    cleartext: true,
  },
  ios: {
    scheme: 'super-shoppe-middleware',
  },
  plugins: {
    CapacitorCookies: {
      enabled: true,
    },
    PrivacyScreen: {
      enable: false,
    },
    PushNotifications: {
      presentationOptions: ['badge', 'sound', 'alert'],
    },
  },
};

export default config;
